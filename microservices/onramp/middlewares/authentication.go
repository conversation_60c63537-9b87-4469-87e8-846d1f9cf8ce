package middlewares

import (
	"context"
	"net/http"
	"strings"

	"github.com/coreos/go-oidc"
	"synapse-its.com/onramp/domain"
	"synapse-its.com/onramp/modules/auth"
	"synapse-its.com/shared/api/response"
	"synapse-its.com/shared/logger"
	"synapse-its.com/shared/rest/onramp/session"
)

func AuthMiddleware(sessionStore session.SessionStore) func(http.Handler) http.Handler {
	return func(next http.Handler) http.Handler {
		return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			ctx := oidc.ClientContext(r.Context(), auth.LocalhostHTTPProxy)
			isDev := strings.HasPrefix(r.Host, "localhost:4200")
			oidcConfig := map[bool]*auth.OIDCConfig{
				false: &auth.SynapseOIDC,
				true:  &auth.SynapseOIDCLocal,
			}[isDev]
			verifier := oidcConfig.Verifier

			// 1) Grab session cookie
			c, err := r.<PERSON>("session_id")
			if err != nil {
				logger.Debugf("error - AuthMiddleware: %v", err.Error())
				response.CreateUnauthorizedResponse(w)
				return
			}

			// 2) Get session data from the injected session store
			sessionData, ok := sessionStore.GetSession(c.Value)
			if !ok {
				logger.Warn("error - AuthMiddleware: invalid session")
				response.CreateUnauthorizedResponse(w)
				return
			}

			// 3) Verify ID Token again (optional if you trust your store)
			rawID := sessionData.OAuthToken.Extra("id_token").(string)
			idToken, err := verifier.Verify(ctx, rawID)
			if err != nil {
				logger.Errorf("error - AuthMiddleware: %v", err.Error())
				response.CreateUnauthorizedResponse(w)
				return
			}

			// 4) Extract claims
			var claims map[string]interface{}
			if err := idToken.Claims(&claims); err != nil {
				logger.Errorf("error - AuthMiddleware: %v", err.Error())
				response.CreateUnauthorizedResponse(w)
				return
			}

			// 5) Next handler with claims in context
			next.ServeHTTP(w, r.WithContext(context.WithValue(r.Context(), domain.UserKey, claims)))
		})
	}
}
