package user

import (
	"net/http"

	"synapse-its.com/shared/api/middleware"
	"synapse-its.com/shared/rest/onramp/session"
	RestUserPermissions "synapse-its.com/shared/rest/onramp/user/permissions"

	"github.com/gorilla/mux"
)

type Handler struct {
	sessionStore session.SessionStore
}

func NewHandler(sessionStore session.SessionStore) *Handler {
	return &Handler{
		sessionStore: sessionStore,
	}
}

func (h *Handler) RegisterRoutes(router *mux.Router) {
	// Apply session middleware to user routes
	userRouter := router.PathPrefix("/user").Subrouter()
	userRouter.Use(middleware.SessionMiddleware(h.sessionStore))

	userRouter.HandleFunc("/permissions", RestUserPermissions.Handler).Methods(http.MethodGet)
}
