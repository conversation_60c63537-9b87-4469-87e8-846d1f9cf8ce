package auth

import (
	"context"
	"errors"
	"testing"
	"time"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"synapse-its.com/onramp/domain"
)

func TestNewService(t *testing.T) {
	t.<PERSON>()

	// Arrange
	mockRepo := &MockAuthRepository{}
	mockHasher := &MockPasswordHasher{}
	mockTokenGen := &MockTokenGenerator{}

	// Act
	service := NewService(mockRepo, mockHasher, mockTokenGen)

	// Assert
	assert.NotNil(t, service)
	assert.Implements(t, (*AuthService)(nil), service)
}

func TestService_BasicAuth(t *testing.T) {
	t.Parallel()

	tests := []struct {
		name           string
		request        *BasicAuthRequest
		setupMocks     func(*MockAuthRepository, *MockPasswordHasher, *MockTokenGenerator)
		expectedResult *LoginResponse
		expectedError  error
	}{
		{
			name: "successful authentication",
			request: &BasicAuthRequest{
				Username: "testuser",
				Password: "password123",
			},
			setupMocks: func(repo *MockAuthRepository, hasher *MockPasswordHasher, tokenGen *MockTokenGenerator) {
				user := createTestUser()
				authMethod := createTestAuthMethod(user.ID)

				repo.GetByUsernameFunc = func(ctx context.Context, username string) (*domain.User, *domain.AuthMethod, error) {
					return user, authMethod, nil
				}

				repo.UpdateLastLoginFunc = func(ctx context.Context, user *domain.User, authMethod *domain.AuthMethod) error {
					return nil
				}

				hasher.ComparePasswordFunc = func(password, hash string) bool {
					return password == "password123" && hash == "hashed_password"
				}

				tokenGen.GenerateTokenFunc = func(username string) (string, time.Time, error) {
					return "jwt_token", time.Now().Add(time.Hour), nil
				}
			},
			expectedResult: &LoginResponse{
				User:  createTestUser(),
				Token: "jwt_token",
			},
			expectedError: nil,
		},
		{
			name: "empty username",
			request: &BasicAuthRequest{
				Username: "",
				Password: "password123",
			},
			setupMocks: func(repo *MockAuthRepository, hasher *MockPasswordHasher, tokenGen *MockTokenGenerator) {
				// No mocks needed for this case
			},
			expectedResult: nil,
			expectedError:  domain.ErrInvalidCredentials,
		},
		{
			name: "empty password",
			request: &BasicAuthRequest{
				Username: "testuser",
				Password: "",
			},
			setupMocks: func(repo *MockAuthRepository, hasher *MockPasswordHasher, tokenGen *MockTokenGenerator) {
				// No mocks needed for this case
			},
			expectedResult: nil,
			expectedError:  domain.ErrInvalidCredentials,
		},
		{
			name: "user not found",
			request: &BasicAuthRequest{
				Username: "nonexistent",
				Password: "password123",
			},
			setupMocks: func(repo *MockAuthRepository, hasher *MockPasswordHasher, tokenGen *MockTokenGenerator) {
				repo.GetByUsernameFunc = func(ctx context.Context, username string) (*domain.User, *domain.AuthMethod, error) {
					return nil, nil, nil
				}
			},
			expectedResult: nil,
			expectedError:  domain.ErrUserNotFound,
		},
		{
			name: "repository error",
			request: &BasicAuthRequest{
				Username: "testuser",
				Password: "password123",
			},
			setupMocks: func(repo *MockAuthRepository, hasher *MockPasswordHasher, tokenGen *MockTokenGenerator) {
				repo.GetByUsernameFunc = func(ctx context.Context, username string) (*domain.User, *domain.AuthMethod, error) {
					return nil, nil, errors.New("database error")
				}
			},
			expectedResult: nil,
			expectedError:  errors.New("database error"),
		},
		{
			name: "user found but auth method missing",
			request: &BasicAuthRequest{
				Username: "testuser",
				Password: "password123",
			},
			setupMocks: func(repo *MockAuthRepository, hasher *MockPasswordHasher, tokenGen *MockTokenGenerator) {
				user := createTestUser()
				repo.GetByUsernameFunc = func(ctx context.Context, username string) (*domain.User, *domain.AuthMethod, error) {
					return user, nil, nil
				}
			},
			expectedResult: nil,
			expectedError:  domain.ErrInvalidCredentials,
		},
		{
			name: "auth method found but user missing",
			request: &BasicAuthRequest{
				Username: "testuser",
				Password: "password123",
			},
			setupMocks: func(repo *MockAuthRepository, hasher *MockPasswordHasher, tokenGen *MockTokenGenerator) {
				authMethod := createTestAuthMethod(uuid.New())
				repo.GetByUsernameFunc = func(ctx context.Context, username string) (*domain.User, *domain.AuthMethod, error) {
					return nil, authMethod, nil
				}
			},
			expectedResult: nil,
			expectedError:  domain.ErrInvalidCredentials,
		},
		{
			name: "account disabled",
			request: &BasicAuthRequest{
				Username: "testuser",
				Password: "password123",
			},
			setupMocks: func(repo *MockAuthRepository, hasher *MockPasswordHasher, tokenGen *MockTokenGenerator) {
				user := createTestUser()
				authMethod := createTestAuthMethod(user.ID)
				authMethod.IsEnabled = false

				repo.GetByUsernameFunc = func(ctx context.Context, username string) (*domain.User, *domain.AuthMethod, error) {
					return user, authMethod, nil
				}
			},
			expectedResult: nil,
			expectedError:  domain.ErrAccountDisabled,
		},
		{
			name: "invalid password",
			request: &BasicAuthRequest{
				Username: "testuser",
				Password: "wrongpassword",
			},
			setupMocks: func(repo *MockAuthRepository, hasher *MockPasswordHasher, tokenGen *MockTokenGenerator) {
				user := createTestUser()
				authMethod := createTestAuthMethod(user.ID)

				repo.GetByUsernameFunc = func(ctx context.Context, username string) (*domain.User, *domain.AuthMethod, error) {
					return user, authMethod, nil
				}

				hasher.ComparePasswordFunc = func(password, hash string) bool {
					return false // Password doesn't match
				}
			},
			expectedResult: nil,
			expectedError:  domain.ErrInvalidCredentials,
		},
		{
			name: "update last login error",
			request: &BasicAuthRequest{
				Username: "testuser",
				Password: "password123",
			},
			setupMocks: func(repo *MockAuthRepository, hasher *MockPasswordHasher, tokenGen *MockTokenGenerator) {
				user := createTestUser()
				authMethod := createTestAuthMethod(user.ID)

				repo.GetByUsernameFunc = func(ctx context.Context, username string) (*domain.User, *domain.AuthMethod, error) {
					return user, authMethod, nil
				}

				repo.UpdateLastLoginFunc = func(ctx context.Context, user *domain.User, authMethod *domain.AuthMethod) error {
					return errors.New("update error")
				}

				hasher.ComparePasswordFunc = func(password, hash string) bool {
					return password == "password123" && hash == "hashed_password"
				}
			},
			expectedResult: nil,
			expectedError:  errors.New("update error"),
		},
		{
			name: "token generation error",
			request: &BasicAuthRequest{
				Username: "testuser",
				Password: "password123",
			},
			setupMocks: func(repo *MockAuthRepository, hasher *MockPasswordHasher, tokenGen *MockTokenGenerator) {
				user := createTestUser()
				authMethod := createTestAuthMethod(user.ID)

				repo.GetByUsernameFunc = func(ctx context.Context, username string) (*domain.User, *domain.AuthMethod, error) {
					return user, authMethod, nil
				}

				repo.UpdateLastLoginFunc = func(ctx context.Context, user *domain.User, authMethod *domain.AuthMethod) error {
					return nil
				}

				hasher.ComparePasswordFunc = func(password, hash string) bool {
					return password == "password123" && hash == "hashed_password"
				}

				tokenGen.GenerateTokenFunc = func(username string) (string, time.Time, error) {
					return "", time.Time{}, errors.New("token generation error")
				}
			},
			expectedResult: nil,
			expectedError:  errors.New("token generation error"),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Arrange
			mockRepo := &MockAuthRepository{}
			mockHasher := &MockPasswordHasher{}
			mockTokenGen := &MockTokenGenerator{}

			if tt.setupMocks != nil {
				tt.setupMocks(mockRepo, mockHasher, mockTokenGen)
			}

			service := NewService(mockRepo, mockHasher, mockTokenGen)
			ctx := context.Background()

			// Act
			result, err := service.BasicAuth(ctx, tt.request)

			// Assert
			if tt.expectedError != nil {
				assert.Error(t, err)
				assert.Equal(t, tt.expectedError.Error(), err.Error())
				assert.Nil(t, result)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, result)
				assert.Equal(t, tt.expectedResult.Token, result.Token)
				assert.NotNil(t, result.User)
			}
		})
	}
}

func TestService_Register(t *testing.T) {
	t.Parallel()

	tests := []struct {
		name          string
		request       *RegisterRequest
		setupMocks    func(*MockAuthRepository, *MockPasswordHasher, *MockTokenGenerator)
		expectedError error
	}{
		{
			name: "successful registration",
			request: &RegisterRequest{
				FirstName: "John",
				LastName:  "Doe",
				Username:  "newuser",
				Password:  "password123",
				Email:     "<EMAIL>",
			},
			setupMocks: func(repo *MockAuthRepository, hasher *MockPasswordHasher, tokenGen *MockTokenGenerator) {
				// User doesn't exist
				repo.GetByUsernameFunc = func(ctx context.Context, username string) (*domain.User, *domain.AuthMethod, error) {
					return nil, nil, nil
				}

				hasher.HashPasswordFunc = func(password string) string {
					return "hashed_" + password
				}

				repo.CreateBasicAuthUserFunc = func(ctx context.Context, user *domain.User, authMethod *domain.AuthMethod) error {
					return nil
				}
			},
			expectedError: nil,
		},
		{
			name: "empty username",
			request: &RegisterRequest{
				FirstName: "John",
				LastName:  "Doe",
				Username:  "",
				Password:  "password123",
				Email:     "<EMAIL>",
			},
			setupMocks: func(repo *MockAuthRepository, hasher *MockPasswordHasher, tokenGen *MockTokenGenerator) {
				// No mocks needed
			},
			expectedError: domain.ErrInvalidInput,
		},
		{
			name: "empty password",
			request: &RegisterRequest{
				FirstName: "John",
				LastName:  "Doe",
				Username:  "newuser",
				Password:  "",
				Email:     "<EMAIL>",
			},
			setupMocks: func(repo *MockAuthRepository, hasher *MockPasswordHasher, tokenGen *MockTokenGenerator) {
				// No mocks needed
			},
			expectedError: domain.ErrInvalidInput,
		},
		{
			name: "user already exists",
			request: &RegisterRequest{
				FirstName: "John",
				LastName:  "Doe",
				Username:  "existinguser",
				Password:  "password123",
				Email:     "<EMAIL>",
			},
			setupMocks: func(repo *MockAuthRepository, hasher *MockPasswordHasher, tokenGen *MockTokenGenerator) {
				user := createTestUser()
				authMethod := createTestAuthMethod(user.ID)
				authMethod.UserName = "existinguser"

				repo.GetByUsernameFunc = func(ctx context.Context, username string) (*domain.User, *domain.AuthMethod, error) {
					return user, authMethod, nil
				}
			},
			expectedError: domain.ErrUserAlreadyExists,
		},
		{
			name: "case insensitive username check",
			request: &RegisterRequest{
				FirstName: "John",
				LastName:  "Doe",
				Username:  "EXISTINGUSER",
				Password:  "password123",
				Email:     "<EMAIL>",
			},
			setupMocks: func(repo *MockAuthRepository, hasher *MockPasswordHasher, tokenGen *MockTokenGenerator) {
				user := createTestUser()
				authMethod := createTestAuthMethod(user.ID)
				authMethod.UserName = "existinguser"

				repo.GetByUsernameFunc = func(ctx context.Context, username string) (*domain.User, *domain.AuthMethod, error) {
					return user, authMethod, nil
				}
			},
			expectedError: domain.ErrUserAlreadyExists,
		},
		{
			name: "create user error",
			request: &RegisterRequest{
				FirstName: "John",
				LastName:  "Doe",
				Username:  "newuser",
				Password:  "password123",
				Email:     "<EMAIL>",
			},
			setupMocks: func(repo *MockAuthRepository, hasher *MockPasswordHasher, tokenGen *MockTokenGenerator) {
				repo.GetByUsernameFunc = func(ctx context.Context, username string) (*domain.User, *domain.AuthMethod, error) {
					return nil, nil, nil
				}

				hasher.HashPasswordFunc = func(password string) string {
					return "hashed_" + password
				}

				repo.CreateBasicAuthUserFunc = func(ctx context.Context, user *domain.User, authMethod *domain.AuthMethod) error {
					return errors.New("database error")
				}
			},
			expectedError: errors.New("database error"),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Arrange
			mockRepo := &MockAuthRepository{}
			mockHasher := &MockPasswordHasher{}
			mockTokenGen := &MockTokenGenerator{}

			if tt.setupMocks != nil {
				tt.setupMocks(mockRepo, mockHasher, mockTokenGen)
			}

			service := NewService(mockRepo, mockHasher, mockTokenGen)
			ctx := context.Background()

			// Act
			err := service.Register(ctx, tt.request)

			// Assert
			if tt.expectedError != nil {
				assert.Error(t, err)
				assert.Equal(t, tt.expectedError.Error(), err.Error())
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

func TestService_HandleOIDCLogin(t *testing.T) {
	t.Parallel()

	tests := []struct {
		name           string
		request        *OIDCLoginRequest
		setupMocks     func(*MockAuthRepository, *MockPasswordHasher, *MockTokenGenerator)
		expectedResult *LoginResponse
		expectedError  error
	}{
		{
			name: "successful OIDC login - existing user",
			request: &OIDCLoginRequest{
				Subject: "test-subject",
				Issuer:  "test-issuer",
				Email:   "<EMAIL>",
				Name:    "John Doe",
			},
			setupMocks: func(repo *MockAuthRepository, hasher *MockPasswordHasher, tokenGen *MockTokenGenerator) {
				user := createTestUser()
				authMethod := &domain.AuthMethod{
					ID:        uuid.New(),
					UserID:    user.ID,
					Type:      domain.AuthMethodTypeOIDC,
					Sub:       "test-subject",
					Issuer:    "test-issuer",
					Email:     "<EMAIL>",
					IsEnabled: true,
				}

				repo.GetByOIDCSubjectFunc = func(ctx context.Context, issuer, subject string) (*domain.User, *domain.AuthMethod, error) {
					return user, authMethod, nil
				}

				repo.UpdateLastLoginFunc = func(ctx context.Context, user *domain.User, authMethod *domain.AuthMethod) error {
					return nil
				}

				tokenGen.GenerateTokenFunc = func(userID string) (string, time.Time, error) {
					return "test-token", time.Now().Add(time.Hour), nil
				}
			},
			expectedResult: &LoginResponse{
				User:  createTestUser(),
				Token: "test-token",
			},
			expectedError: nil,
		},
		{
			name: "successful OIDC login - new user with full name",
			request: &OIDCLoginRequest{
				Subject: "new-subject",
				Issuer:  "test-issuer",
				Email:   "<EMAIL>",
				Name:    "Jane Smith",
			},
			setupMocks: func(repo *MockAuthRepository, hasher *MockPasswordHasher, tokenGen *MockTokenGenerator) {
				// User doesn't exist
				repo.GetByOIDCSubjectFunc = func(ctx context.Context, issuer, subject string) (*domain.User, *domain.AuthMethod, error) {
					return nil, nil, nil
				}

				repo.CreateOIDCUserFunc = func(ctx context.Context, user *domain.User, authMethod *domain.AuthMethod) error {
					// Simulate setting the user ID
					user.ID = uuid.New()
					authMethod.UserID = user.ID
					return nil
				}

				tokenGen.GenerateTokenFunc = func(userID string) (string, time.Time, error) {
					return "new-token", time.Now().Add(time.Hour), nil
				}
			},
			expectedResult: &LoginResponse{
				User: &domain.User{
					ID:        uuid.Nil, // Will be set by CreateOIDCUser
					FirstName: "Jane",
					LastName:  "Smith",
				},
				Token: "new-token",
			},
			expectedError: nil,
		},
		{
			name: "successful OIDC login - new user with single name",
			request: &OIDCLoginRequest{
				Subject: "new-subject",
				Issuer:  "test-issuer",
				Email:   "<EMAIL>",
				Name:    "John",
			},
			setupMocks: func(repo *MockAuthRepository, hasher *MockPasswordHasher, tokenGen *MockTokenGenerator) {
				// User doesn't exist
				repo.GetByOIDCSubjectFunc = func(ctx context.Context, issuer, subject string) (*domain.User, *domain.AuthMethod, error) {
					return nil, nil, nil
				}

				repo.CreateOIDCUserFunc = func(ctx context.Context, user *domain.User, authMethod *domain.AuthMethod) error {
					// Simulate setting the user ID
					user.ID = uuid.New()
					authMethod.UserID = user.ID
					return nil
				}

				tokenGen.GenerateTokenFunc = func(userID string) (string, time.Time, error) {
					return "new-token", time.Now().Add(time.Hour), nil
				}
			},
			expectedResult: &LoginResponse{
				User: &domain.User{
					ID:        uuid.Nil, // Will be set by CreateOIDCUser
					FirstName: "John",
					LastName:  "",
				},
				Token: "new-token",
			},
			expectedError: nil,
		},
		{
			name: "successful OIDC login - new user with empty name",
			request: &OIDCLoginRequest{
				Subject: "new-subject",
				Issuer:  "test-issuer",
				Email:   "<EMAIL>",
				Name:    "",
			},
			setupMocks: func(repo *MockAuthRepository, hasher *MockPasswordHasher, tokenGen *MockTokenGenerator) {
				// User doesn't exist
				repo.GetByOIDCSubjectFunc = func(ctx context.Context, issuer, subject string) (*domain.User, *domain.AuthMethod, error) {
					return nil, nil, nil
				}

				repo.CreateOIDCUserFunc = func(ctx context.Context, user *domain.User, authMethod *domain.AuthMethod) error {
					// Simulate setting the user ID
					user.ID = uuid.New()
					authMethod.UserID = user.ID
					return nil
				}

				tokenGen.GenerateTokenFunc = func(userID string) (string, time.Time, error) {
					return "new-token", time.Now().Add(time.Hour), nil
				}
			},
			expectedResult: &LoginResponse{
				User: &domain.User{
					ID:        uuid.Nil, // Will be set by CreateOIDCUser
					FirstName: "",
					LastName:  "",
				},
				Token: "new-token",
			},
			expectedError: nil,
		},
		{
			name: "empty subject",
			request: &OIDCLoginRequest{
				Subject: "",
				Issuer:  "test-issuer",
				Email:   "<EMAIL>",
				Name:    "John Doe",
			},
			setupMocks: func(repo *MockAuthRepository, hasher *MockPasswordHasher, tokenGen *MockTokenGenerator) {
				// No mocks needed for validation error
			},
			expectedResult: nil,
			expectedError:  domain.ErrInvalidCredentials,
		},
		{
			name: "empty issuer",
			request: &OIDCLoginRequest{
				Subject: "test-subject",
				Issuer:  "",
				Email:   "<EMAIL>",
				Name:    "John Doe",
			},
			setupMocks: func(repo *MockAuthRepository, hasher *MockPasswordHasher, tokenGen *MockTokenGenerator) {
				// No mocks needed for validation error
			},
			expectedResult: nil,
			expectedError:  domain.ErrInvalidCredentials,
		},
		{
			name: "repository error when getting user",
			request: &OIDCLoginRequest{
				Subject: "test-subject",
				Issuer:  "test-issuer",
				Email:   "<EMAIL>",
				Name:    "John Doe",
			},
			setupMocks: func(repo *MockAuthRepository, hasher *MockPasswordHasher, tokenGen *MockTokenGenerator) {
				repo.GetByOIDCSubjectFunc = func(ctx context.Context, issuer, subject string) (*domain.User, *domain.AuthMethod, error) {
					return nil, nil, errors.New("database error")
				}
			},
			expectedResult: nil,
			expectedError:  errors.New("database error"),
		},
		{
			name: "error creating new OIDC user",
			request: &OIDCLoginRequest{
				Subject: "new-subject",
				Issuer:  "test-issuer",
				Email:   "<EMAIL>",
				Name:    "John Doe",
			},
			setupMocks: func(repo *MockAuthRepository, hasher *MockPasswordHasher, tokenGen *MockTokenGenerator) {
				// User doesn't exist
				repo.GetByOIDCSubjectFunc = func(ctx context.Context, issuer, subject string) (*domain.User, *domain.AuthMethod, error) {
					return nil, nil, nil
				}

				repo.CreateOIDCUserFunc = func(ctx context.Context, user *domain.User, authMethod *domain.AuthMethod) error {
					return errors.New("failed to create user")
				}
			},
			expectedResult: nil,
			expectedError:  errors.New("failed to create user"),
		},
		{
			name: "error updating last login for existing user",
			request: &OIDCLoginRequest{
				Subject: "test-subject",
				Issuer:  "test-issuer",
				Email:   "<EMAIL>",
				Name:    "John Doe",
			},
			setupMocks: func(repo *MockAuthRepository, hasher *MockPasswordHasher, tokenGen *MockTokenGenerator) {
				user := createTestUser()
				authMethod := &domain.AuthMethod{
					ID:        uuid.New(),
					UserID:    user.ID,
					Type:      domain.AuthMethodTypeOIDC,
					Sub:       "test-subject",
					Issuer:    "test-issuer",
					Email:     "<EMAIL>",
					IsEnabled: true,
				}

				repo.GetByOIDCSubjectFunc = func(ctx context.Context, issuer, subject string) (*domain.User, *domain.AuthMethod, error) {
					return user, authMethod, nil
				}

				repo.UpdateLastLoginFunc = func(ctx context.Context, user *domain.User, authMethod *domain.AuthMethod) error {
					return errors.New("failed to update last login")
				}
			},
			expectedResult: nil,
			expectedError:  errors.New("failed to update last login"),
		},
		{
			name: "error generating token",
			request: &OIDCLoginRequest{
				Subject: "test-subject",
				Issuer:  "test-issuer",
				Email:   "<EMAIL>",
				Name:    "John Doe",
			},
			setupMocks: func(repo *MockAuthRepository, hasher *MockPasswordHasher, tokenGen *MockTokenGenerator) {
				user := createTestUser()
				authMethod := &domain.AuthMethod{
					ID:        uuid.New(),
					UserID:    user.ID,
					Type:      domain.AuthMethodTypeOIDC,
					Sub:       "test-subject",
					Issuer:    "test-issuer",
					Email:     "<EMAIL>",
					IsEnabled: true,
				}

				repo.GetByOIDCSubjectFunc = func(ctx context.Context, issuer, subject string) (*domain.User, *domain.AuthMethod, error) {
					return user, authMethod, nil
				}

				repo.UpdateLastLoginFunc = func(ctx context.Context, user *domain.User, authMethod *domain.AuthMethod) error {
					return nil
				}

				tokenGen.GenerateTokenFunc = func(userID string) (string, time.Time, error) {
					return "", time.Time{}, errors.New("token generation failed")
				}
			},
			expectedResult: nil,
			expectedError:  errors.New("token generation failed"),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Create mocks
			repo := &MockAuthRepository{}
			hasher := &MockPasswordHasher{}
			tokenGen := &MockTokenGenerator{}

			// Setup mocks
			if tt.setupMocks != nil {
				tt.setupMocks(repo, hasher, tokenGen)
			}

			// Create service
			service := NewService(repo, hasher, tokenGen)

			// Execute test
			result, err := service.HandleOIDCLogin(context.Background(), tt.request)

			// Assert results
			if tt.expectedError != nil {
				assert.Error(t, err)
				assert.Equal(t, tt.expectedError.Error(), err.Error())
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, result)
				assert.Equal(t, tt.expectedResult.Token, result.Token)
				assert.Equal(t, tt.expectedResult.User.FirstName, result.User.FirstName)
				assert.Equal(t, tt.expectedResult.User.LastName, result.User.LastName)
			}
		})
	}
}
