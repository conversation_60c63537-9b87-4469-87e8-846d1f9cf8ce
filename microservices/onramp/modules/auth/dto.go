package auth

import (
	"golang.org/x/oauth2"
	"synapse-its.com/onramp/domain"
	"synapse-its.com/shared/api/authorizer"
)

type BasicAuthRequest struct {
	Username string `json:"username"`
	Password string `json:"password"`
}

type LoginResponse struct {
	User  *domain.User `json:"user"`
	Token string       `json:"token"`
}

type RegisterRequest struct {
	FirstName string `json:"firstName"`
	LastName  string `json:"lastName"`
	Username  string `json:"username"`
	Password  string `json:"password"`
	Email     string `json:"email"`
}

type OIDCLoginRequest struct {
	Subject string `json:"sub"`
	Issuer  string `json:"iss"`
	Email   string `json:"email"`
	Name    string `json:"name"`
}

// OAuth2CallbackRequest contains the data needed to process OAuth2 callback
type OAuth2CallbackRequest struct {
	Code        string                 `json:"code"`
	State       string                 `json:"state"`
	StateCookie string                 `json:"state_cookie"`
	IsDev       bool                   `json:"is_dev"`
	Claims      map[string]interface{} `json:"claims"`
	OAuthToken  *oauth2.Token          `json:"oauth_token"`
}

// OAuth2CallbackResponse contains the result of OAuth2 callback processing
type OAuth2CallbackResponse struct {
	User            *domain.User                `json:"user"`
	Token           string                      `json:"token"`
	UserPermissions *authorizer.UserPermissions `json:"user_permissions"`
	OAuthToken      *oauth2.Token               `json:"oauth_token"`
}
