package auth

import "synapse-its.com/onramp/domain"

type BasicAuthRequest struct {
	Username string `json:"username"`
	Password string `json:"password"`
}

type LoginResponse struct {
	User  *domain.User `json:"user"`
	Token string       `json:"token"`
}

type RegisterRequest struct {
	FirstName string `json:"firstName"`
	LastName  string `json:"lastName"`
	Username  string `json:"username"`
	Password  string `json:"password"`
	Email     string `json:"email"`
}

type OIDCLoginRequest struct {
	Subject string `json:"sub"`
	Issuer  string `json:"iss"`
	Email   string `json:"email"`
	Name    string `json:"name"`
}
