package auth

import (
	"context"
	"database/sql"
	"encoding/json"
	"fmt"
	"time"

	"github.com/google/uuid"
	"synapse-its.com/onramp/domain"
	"synapse-its.com/shared/connect"
)

type storage struct {
	db connect.DatabaseExecutor
}

func NewPostgresAuthRepository(db connect.DatabaseExecutor) domain.AuthRepository {
	return &storage{
		db: db,
	}
}

func (s *storage) GetByUsername(ctx context.Context, username string) (*domain.User, *domain.AuthMethod, error) {
	query := `
		SELECT 
			u.Id as user_id,
			u.FirstName as user_firstname,
			u.LastName as user_lastname,
			u.Mobile as user_mobile,
			u.IanaTimezone as user_ianatimezone,
			u.Description as user_description,
			am.Id as authmethod_id,
			am.UserId as authmethod_userid,
			am.Type as authmethod_type,
			am.Sub as authmethod_sub,
			am.Issuer as authmethod_issuer,
			am.UserName as authmethod_username,
			am.PasswordHash as authmethod_passwordhash,
			am.Email as authmethod_email,
			am.Metadata as authmethod_metadata,
			am.IsEnabled as authmethod_isenabled
		FROM {{User}} u
		INNER JOIN {{AuthMethod}} am ON u.Id = am.UserId
		WHERE am.UserName = $1 
		AND am.Type = 'USERNAME_PASSWORD'
		AND am.IsDeleted = false
		AND u.IsDeleted = false
		LIMIT 1`

	var dbResult UserAuthMethod
	err := s.db.QueryRowStruct(&dbResult, query, username)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, nil, nil
		}
		return nil, nil, err
	}

	// Convert to domain objects
	user := &domain.User{
		ID:           dbResult.UserID,
		FirstName:    stringValue(dbResult.FirstName),
		LastName:     stringValue(dbResult.LastName),
		Mobile:       stringValue(dbResult.Mobile),
		IanaTimezone: dbResult.IanaTimezone,
		Description:  stringValue(dbResult.Description),
	}

	authMethod := &domain.AuthMethod{
		ID:           dbResult.AuthMethodID,
		UserID:       dbResult.AuthMethodUserID,
		Type:         domain.AuthMethodType(dbResult.Type),
		Sub:          stringValue(dbResult.Sub),
		Issuer:       stringValue(dbResult.Issuer),
		UserName:     stringValue(dbResult.UserName),
		PasswordHash: stringValue(dbResult.PasswordHash),
		Email:        stringValue(dbResult.Email),
		IsEnabled:    dbResult.IsEnabled,
	}

	// Parse metadata JSON
	if dbResult.Metadata != nil {
		if err := json.Unmarshal(dbResult.Metadata, &authMethod.Metadata); err != nil {
			// If metadata parsing fails, set to empty map
			authMethod.Metadata = make(map[string]interface{})
		}
	} else {
		authMethod.Metadata = make(map[string]interface{})
	}

	return user, authMethod, nil
}

func (s *storage) UpdateLastLogin(ctx context.Context, user *domain.User, authMethod *domain.AuthMethod) error {
	now := time.Now()

	// Update both User.LastLogin and AuthMethod.LastLogin
	query := `
		UPDATE {{User}} 
		SET LastLogin = $1, UpdatedAt = $1 
		WHERE Id = $2`

	_, err := s.db.Exec(query, now, user.ID)
	if err != nil {
		return err
	}

	query = `
		UPDATE {{AuthMethod}} 
		SET LastLogin = $1, UpdatedAt = $1 
		WHERE Id = $2`

	_, err = s.db.Exec(query, now, authMethod.ID)
	return err
}

func (s *storage) CreateBasicAuthUser(ctx context.Context, user *domain.User, authMethod *domain.AuthMethod) error {
	// Insert user record and get the auto-generated ID
	userQuery := `
		INSERT INTO {{User}} (
			FirstName, 
			LastName 
		) VALUES (
			$1, $2
		)
		RETURNING Id`

	row, err := s.db.QueryRow(userQuery,
		user.FirstName,
		user.LastName,
	)
	if err != nil {
		return err
	}

	// Extract the userID from the returned row
	// PostgreSQL returns column names in lowercase unless quoted
	userIDInterface, ok := row["id"]
	if !ok {
		return fmt.Errorf("ID column not returned from user insert")
	}

	userID, ok := userIDInterface.(string)
	if !ok {
		return fmt.Errorf("ID column is not a string: %T", userIDInterface)
	}

	// Update the user object with the generated ID
	parsedUUID, parseErr := uuid.Parse(userID)
	if parseErr != nil {
		return fmt.Errorf("failed to parse user ID as UUID: %w", parseErr)
	}
	user.ID = parsedUUID

	// Update the auth method object with the user ID
	authMethod.UserID = parsedUUID

	// Insert auth method record (AuthMethod ID will be auto-generated)
	authMethodQuery := `
		INSERT INTO {{AuthMethod}} (
			UserId, 
			Type, 
			UserName, 
			PasswordHash, 
			Email 
		) VALUES (
			$1, $2, $3, $4, $5
		)`

	_, err = s.db.Exec(authMethodQuery,
		userID,
		authMethod.Type,
		authMethod.UserName,
		authMethod.PasswordHash,
		authMethod.Email,
	)
	if err != nil {
		return err
	}

	return nil
}

func (s *storage) GetByOIDCSubject(ctx context.Context, issuer, subject string) (*domain.User, *domain.AuthMethod, error) {
	query := `
		SELECT 
			u.Id as user_id,
			u.FirstName as user_firstname,
			u.LastName as user_lastname,
			u.Mobile as user_mobile,
			u.IanaTimezone as user_ianatimezone,
			u.Description as user_description,
			am.Id as authmethod_id,
			am.UserId as authmethod_userid,
			am.Type as authmethod_type,
			am.Sub as authmethod_sub,
			am.Issuer as authmethod_issuer,
			am.UserName as authmethod_username,
			am.PasswordHash as authmethod_passwordhash,
			am.Email as authmethod_email,
			am.Metadata as authmethod_metadata,
			am.IsEnabled as authmethod_isenabled
		FROM {{User}} u
		INNER JOIN {{AuthMethod}} am ON u.Id = am.UserId
		WHERE am.Issuer = $1 
		AND am.Sub = $2
		AND am.Type = 'OIDC'
		AND am.IsDeleted = false
		AND u.IsDeleted = false
		LIMIT 1`

	var dbResult UserAuthMethod
	err := s.db.QueryRowStruct(&dbResult, query, issuer, subject)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, nil, nil
		}
		return nil, nil, err
	}

	// Convert to domain objects
	user := &domain.User{
		ID:           dbResult.UserID,
		FirstName:    stringValue(dbResult.FirstName),
		LastName:     stringValue(dbResult.LastName),
		Mobile:       stringValue(dbResult.Mobile),
		IanaTimezone: dbResult.IanaTimezone,
		Description:  stringValue(dbResult.Description),
	}

	authMethod := &domain.AuthMethod{
		ID:           dbResult.AuthMethodID,
		UserID:       dbResult.AuthMethodUserID,
		Type:         domain.AuthMethodType(dbResult.Type),
		Sub:          stringValue(dbResult.Sub),
		Issuer:       stringValue(dbResult.Issuer),
		UserName:     stringValue(dbResult.UserName),
		PasswordHash: stringValue(dbResult.PasswordHash),
		Email:        stringValue(dbResult.Email),
		IsEnabled:    dbResult.IsEnabled,
	}

	// Parse metadata JSON
	if dbResult.Metadata != nil {
		if err := json.Unmarshal(dbResult.Metadata, &authMethod.Metadata); err != nil {
			// If metadata parsing fails, set to empty map
			authMethod.Metadata = make(map[string]interface{})
		}
	} else {
		authMethod.Metadata = make(map[string]interface{})
	}

	return user, authMethod, nil
}

func (s *storage) CreateOIDCUser(ctx context.Context, user *domain.User, authMethod *domain.AuthMethod) error {
	// Insert user record and get the auto-generated ID
	userQuery := `
		INSERT INTO {{User}} (
			FirstName, 
			LastName,
			LastLogin
		) VALUES (
			$1, $2, NOW()
		)
		RETURNING Id`

	row, err := s.db.QueryRow(userQuery,
		user.FirstName,
		user.LastName,
	)
	if err != nil {
		return err
	}

	// Extract the userID from the returned row
	// PostgreSQL returns column names in lowercase unless quoted
	userIDInterface, ok := row["id"]
	if !ok {
		return fmt.Errorf("ID column not returned from user insert")
	}

	userID, ok := userIDInterface.(string)
	if !ok {
		return fmt.Errorf("ID column is not a string: %T", userIDInterface)
	}

	// Update the user object with the generated ID
	parsedUUID, parseErr := uuid.Parse(userID)
	if parseErr != nil {
		return fmt.Errorf("failed to parse user ID as UUID: %w", parseErr)
	}
	user.ID = parsedUUID

	// Update the auth method object with the user ID
	authMethod.UserID = parsedUUID

	// Insert auth method record (AuthMethod ID will be auto-generated)
	authMethodQuery := `
		INSERT INTO {{AuthMethod}} (
			UserId, 
			Type, 
			Sub,
			Issuer,
			Email 
		) VALUES (
			$1, $2, $3, $4, $5
		)`

	_, err = s.db.Exec(authMethodQuery,
		userID,
		authMethod.Type,
		authMethod.Sub,
		authMethod.Issuer,
		authMethod.Email,
	)
	if err != nil {
		return err
	}

	return nil
}

// stringValue safely converts a pointer to string to a string value
func stringValue(s *string) string {
	if s == nil {
		return ""
	}
	return *s
}
