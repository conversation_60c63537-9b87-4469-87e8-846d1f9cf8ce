package auth

import (
	"context"
	"time"

	"github.com/google/uuid"
	"synapse-its.com/onramp/domain"
)

// =============================================================================
// AUTH REPOSITORY MOCK
// =============================================================================

// MockAuthRepository mocks domain.AuthRepository
type MockAuthRepository struct {
	GetByUsernameFunc       func(ctx context.Context, username string) (*domain.User, *domain.AuthMethod, error)
	UpdateLastLoginFunc     func(ctx context.Context, user *domain.User, authMethod *domain.AuthMethod) error
	CreateBasicAuthUserFunc func(ctx context.Context, user *domain.User, authMethod *domain.AuthMethod) error
	GetByOIDCSubjectFunc    func(ctx context.Context, issuer, subject string) (*domain.User, *domain.AuthMethod, error)
	CreateOIDCUserFunc      func(ctx context.Context, user *domain.User, authMethod *domain.AuthMethod) error
}

func (m *MockAuthRepository) GetByUsername(ctx context.Context, username string) (*domain.User, *domain.AuthMethod, error) {
	if m.GetByUsernameFunc != nil {
		return m.GetByUsernameFunc(ctx, username)
	}
	return nil, nil, nil
}

func (m *MockAuthRepository) UpdateLastLogin(ctx context.Context, user *domain.User, authMethod *domain.AuthMethod) error {
	if m.UpdateLastLoginFunc != nil {
		return m.UpdateLastLoginFunc(ctx, user, authMethod)
	}
	return nil
}

func (m *MockAuthRepository) CreateBasicAuthUser(ctx context.Context, user *domain.User, authMethod *domain.AuthMethod) error {
	if m.CreateBasicAuthUserFunc != nil {
		return m.CreateBasicAuthUserFunc(ctx, user, authMethod)
	}
	return nil
}

func (m *MockAuthRepository) GetByOIDCSubject(ctx context.Context, issuer, subject string) (*domain.User, *domain.AuthMethod, error) {
	if m.GetByOIDCSubjectFunc != nil {
		return m.GetByOIDCSubjectFunc(ctx, issuer, subject)
	}
	return nil, nil, nil
}

func (m *MockAuthRepository) CreateOIDCUser(ctx context.Context, user *domain.User, authMethod *domain.AuthMethod) error {
	if m.CreateOIDCUserFunc != nil {
		return m.CreateOIDCUserFunc(ctx, user, authMethod)
	}
	return nil
}

// =============================================================================
// PASSWORD HASHER MOCK
// =============================================================================

// MockPasswordHasher mocks pkg.PasswordHasher
type MockPasswordHasher struct {
	HashPasswordFunc    func(password string) string
	ComparePasswordFunc func(password, hash string) bool
}

func (m *MockPasswordHasher) HashPassword(password string) string {
	if m.HashPasswordFunc != nil {
		return m.HashPasswordFunc(password)
	}
	return "hashed_" + password
}

func (m *MockPasswordHasher) ComparePassword(password, hash string) bool {
	if m.ComparePasswordFunc != nil {
		return m.ComparePasswordFunc(password, hash)
	}
	return password == hash
}

// =============================================================================
// TOKEN GENERATOR MOCK
// =============================================================================

// MockTokenGenerator mocks pkg.TokenGenerator
type MockTokenGenerator struct {
	GenerateTokenFunc func(username string) (string, time.Time, error)
}

func (m *MockTokenGenerator) GenerateToken(username string) (string, time.Time, error) {
	if m.GenerateTokenFunc != nil {
		return m.GenerateTokenFunc(username)
	}
	return "fake_token", time.Now().Add(time.Hour), nil
}

// =============================================================================
// TEST HELPER FUNCTIONS
// =============================================================================

// createTestUser creates a test user for testing
func createTestUser() *domain.User {
	return &domain.User{
		ID:           uuid.New(),
		FirstName:    "John",
		LastName:     "Doe",
		Mobile:       "+1234567890",
		IanaTimezone: "America/Chicago",
		Description:  "Test user",
	}
}

// createTestAuthMethod creates a test auth method for testing
func createTestAuthMethod(userID uuid.UUID) *domain.AuthMethod {
	return &domain.AuthMethod{
		ID:           uuid.New(),
		UserID:       userID,
		Type:         domain.AuthMethodTypeUsernamePassword,
		Sub:          "",
		Issuer:       "",
		UserName:     "testuser",
		PasswordHash: "hashed_password",
		Email:        "<EMAIL>",
		Metadata:     make(map[string]interface{}),
		IsEnabled:    true,
	}
}

// createTestUserAuthMethod creates a test UserAuthMethod for database testing
func createTestUserAuthMethod() UserAuthMethod {
	// Use deterministic UUIDs for testing
	userID := uuid.MustParse("12345678-1234-1234-1234-123456789abc")
	authMethodID := uuid.MustParse("*************-4321-4321-cba987654321")

	firstName := "John"
	lastName := "Doe"
	mobile := "+1234567890"
	description := "Test user"
	sub := ""
	issuer := ""
	userName := "testuser"
	passwordHash := "hashed_password"
	email := "<EMAIL>"

	return UserAuthMethod{
		UserID:       userID,
		FirstName:    &firstName,
		LastName:     &lastName,
		Mobile:       &mobile,
		IanaTimezone: "America/Chicago",
		Description:  &description,

		AuthMethodID:     authMethodID,
		AuthMethodUserID: userID,
		Type:             "USERNAME_PASSWORD",
		Sub:              &sub,
		Issuer:           &issuer,
		UserName:         &userName,
		PasswordHash:     &passwordHash,
		Email:            &email,
		Metadata:         []byte(`{"key":"value"}`),
		IsEnabled:        true,
	}
}
