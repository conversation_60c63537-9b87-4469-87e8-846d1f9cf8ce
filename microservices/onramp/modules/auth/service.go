package auth

import (
	"context"
	"strings"

	"synapse-its.com/onramp/domain"
	"synapse-its.com/onramp/pkg"
)

// AuthService defines the interface for authentication business logic
type AuthService interface {
	BasicAuth(ctx context.Context, req *BasicAuthRequest) (*LoginResponse, error)
	Register(ctx context.Context, req *RegisterRequest) error
	HandleOIDCLogin(ctx context.Context, req *OIDCLoginRequest) (*LoginResponse, error)
	ProcessOAuth2Callback(ctx context.Context, req *OAuth2CallbackRequest) (*OAuth2CallbackResponse, error)
}

// SessionService defines the interface for session management
type SessionService interface {
	CreateSessionWithPermissions(ctx context.Context, userID string, oauthToken interface{}) (*SessionCreationResponse, error)
}

// SessionCreationResponse contains session creation result
type SessionCreationResponse struct {
	SessionID       string
	UserPermissions interface{}
}

// service implements the AuthService interface
type service struct {
	authRepo       domain.AuthRepository
	passwordHasher pkg.PasswordHasher
	tokenGenerator pkg.TokenGenerator
}

// NewService creates a new auth service instance
func NewService(authRepo domain.AuthRepository, passwordHasher pkg.PasswordHasher, tokenGenerator pkg.TokenGenerator) AuthService {
	return &service{
		authRepo:       authRepo,
		passwordHasher: passwordHasher,
		tokenGenerator: tokenGenerator,
	}
}

// BasicAuth authenticates a user with username and password
func (s *service) BasicAuth(ctx context.Context, req *BasicAuthRequest) (*LoginResponse, error) {
	// Validate input
	if req.Username == "" || req.Password == "" {
		return nil, domain.ErrInvalidCredentials
	}

	// Get user and auth method by username
	user, authMethod, err := s.authRepo.GetByUsername(ctx, req.Username)
	if err != nil {
		return nil, err
	}

	if user == nil && authMethod == nil {
		return nil, domain.ErrUserNotFound
	}

	if user == nil || authMethod == nil {
		return nil, domain.ErrInvalidCredentials
	}

	// Check if auth method is enabled
	if !authMethod.IsEnabled {
		return nil, domain.ErrAccountDisabled
	}

	// Verify password
	if !s.passwordHasher.ComparePassword(req.Password, authMethod.PasswordHash) {
		return nil, domain.ErrInvalidCredentials
	}

	// Update last login
	if err = s.authRepo.UpdateLastLogin(ctx, user, authMethod); err != nil {
		return nil, err
	}

	// Generate a token
	token, _, err := s.tokenGenerator.GenerateToken(req.Username)
	if err != nil {
		return nil, err
	}

	// TODO: Store the LoginResponse in the session store and set the cookie for FE
	return &LoginResponse{
		User:  user,
		Token: token,
	}, nil
}

func (s *service) Register(ctx context.Context, req *RegisterRequest) error {
	// Validate input
	if req.Username == "" || req.Password == "" {
		return domain.ErrInvalidInput
	}

	// Check if user already exists
	checkUserExists := func() bool {
		existingUser, existingAuthMethod, _ := s.authRepo.GetByUsername(ctx, req.Username)
		if existingUser == nil || existingAuthMethod == nil {
			return false
		}
		return strings.EqualFold(existingAuthMethod.UserName, req.Username)
	}

	if checkUserExists() {
		return domain.ErrUserAlreadyExists
	}

	// Hash the password
	passwordHash := s.passwordHasher.HashPassword(req.Password)

	// Create domain objects
	user := &domain.User{
		// User ID will be set by the database in CreateBasicAuthUser
		FirstName: req.FirstName,
		LastName:  req.LastName,
	}

	authMethod := &domain.AuthMethod{
		// AuthMethodID will be auto-generated by database
		// UserID will be set after user creation
		Type:         domain.AuthMethodTypeUsernamePassword,
		UserName:     req.Username,
		PasswordHash: passwordHash,
		Email:        req.Email,
	}

	// Create user in database (this will populate user.ID with the database-generated UUID)
	if err := s.authRepo.CreateBasicAuthUser(ctx, user, authMethod); err != nil {
		return err
	}

	return nil
}

// HandleOIDCLogin processes OIDC authentication, creating users if they don't exist
func (s *service) HandleOIDCLogin(ctx context.Context, req *OIDCLoginRequest) (*LoginResponse, error) {
	// Validate input
	if req.Subject == "" || req.Issuer == "" {
		return nil, domain.ErrInvalidCredentials
	}

	// Check if user already exists by OIDC subject
	user, authMethod, err := s.authRepo.GetByOIDCSubject(ctx, req.Issuer, req.Subject)
	if err != nil {
		return nil, err
	}

	// If user doesn't exist, create them
	if user == nil || authMethod == nil {
		// Parse name into first/last name
		firstName := ""
		lastName := ""
		if req.Name != "" {
			nameParts := strings.Fields(req.Name)
			if len(nameParts) > 0 {
				firstName = nameParts[0]
			}
			if len(nameParts) > 1 {
				lastName = strings.Join(nameParts[1:], " ")
			}
		}

		// Create new user
		user = &domain.User{
			// User ID will be set by the database in CreateOIDCUser
			FirstName: firstName,
			LastName:  lastName,
		}

		// Create new OIDC auth method
		authMethod = &domain.AuthMethod{
			// AuthMethodID will be auto-generated by database
			// UserID will be set after user creation
			Type:   domain.AuthMethodTypeOIDC,
			Sub:    req.Subject,
			Issuer: req.Issuer,
			Email:  req.Email,
		}

		// Create user and auth method in database
		if err := s.authRepo.CreateOIDCUser(ctx, user, authMethod); err != nil {
			return nil, err
		}
	} else {
		// Update last login for existing user
		if err = s.authRepo.UpdateLastLogin(ctx, user, authMethod); err != nil {
			return nil, err
		}
	}

	// Generate a token (reusing existing token generation)
	token, _, err := s.tokenGenerator.GenerateToken(user.ID.String())
	if err != nil {
		return nil, err
	}

	return &LoginResponse{
		User:  user,
		Token: token,
	}, nil
}

// ProcessOAuth2Callback handles the OAuth2 callback business logic
func (s *service) ProcessOAuth2Callback(ctx context.Context, req *OAuth2CallbackRequest) (*OAuth2CallbackResponse, error) {
	// Validate state
	if req.State != req.StateCookie {
		return nil, domain.ErrInvalidCredentials
	}

	// Extract required claims
	sub, ok := req.Claims["sub"].(string)
	if !ok {
		return nil, domain.ErrInvalidCredentials
	}

	iss, ok := req.Claims["iss"].(string)
	if !ok {
		return nil, domain.ErrInvalidCredentials
	}

	email, _ := req.Claims["email"].(string)
	name, _ := req.Claims["name"].(string)

	// Create OIDC login request
	oidcReq := &OIDCLoginRequest{
		Subject: sub,
		Issuer:  iss,
		Email:   email,
		Name:    name,
	}

	// Handle OIDC login (create user if needed)
	loginResp, err := s.HandleOIDCLogin(ctx, oidcReq)
	if err != nil {
		return nil, err
	}

	return &OAuth2CallbackResponse{
		User:       loginResp.User,
		Token:      loginResp.Token,
		OAuthToken: req.OAuthToken,
	}, nil
}
