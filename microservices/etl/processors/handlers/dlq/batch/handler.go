package dlqBqBatch

import (
	"context"
	"encoding/json"
	"fmt"

	"cloud.google.com/go/pubsub"
	"synapse-its.com/shared/bqbatch"
	"synapse-its.com/shared/connect"
	"synapse-its.com/shared/logger"
	"synapse-its.com/shared/pubsubdata"
	"synapse-its.com/shared/schemas"
)

// --- Dependency abstractions for injection and testing ---
type (
	ConnectorFunc       func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error)
	ParseAttributesFunc func(attrs map[string]string) (pubsubdata.CommonAttributes, pubsubdata.HeaderDetails, error)
	BatchGetter         func(ctx context.Context) (bqbatch.Batcher, error)
)

// HandlerDeps bundles all external dependencies.
type HandlerDeps struct {
	Connector       ConnectorFunc
	ParseAttributes ParseAttributesFunc
	GetBatch        BatchGetter
}

// dlqBqBatch specifically handles batch load failures
func HandlerWithDeps(deps HandlerDeps) func(ctx context.Context, sub connect.PsSubscription) {
	return func(ctx context.Context, sub connect.PsSubscription) {
		batch, batchErr := deps.GetBatch(ctx)
		if batchErr != nil {
			logger.Errorf("Error getting batch: %v", batchErr)
			return
		}
		err := sub.Receive(ctx, func(ctx context.Context, msg *pubsub.Message) {
			logger.Debugf("Received DLQ retry message on %s ID: %s", sub.ID(), msg.ID)

			var failed bqbatch.FailedBatch
			if errUm := json.Unmarshal(msg.Data, &failed); errUm != nil {
				// If this path is being hit, message is malformed and will never be automatically recovered
				// Just dump to generic DLQ table
				logger.Errorf("Error parsing DLQ batch, sending message to DlqMessages: %v", errUm)
				if errBa := batch.Add(schemas.DlqMessages{
					Topic:           sub.ID(),                                                   // Set topic to this subscription name to indicate DLQ issue
					Data:            msg.Data,                                                   // Just a dump of the whole original payload
					DLQReason:       fmt.Sprintf("Error parsing Batch JSON wrapper: %v", errUm), // Set new error reason
					PubsubTimestamp: msg.PublishTime,
					PubsubID:        msg.ID,
				}); errBa != nil {
					msg.Nack()
					return
				}
				msg.Ack()
				return
			}

			// Attempt reprocessing
			logger.Warnf("Retrying DLQ batch for table %s with %d rows (retry count: %d)", failed.Table, len(failed.Rows), failed.RetryCount)
			errLb := batch.LoadBatch(failed)
			if errLb != nil {
				logger.Errorf("Error parsing DLQ batch, sending message to DlqMessages: %v", errLb)
				if errBa := batch.Add(schemas.DlqMessages{
					Topic:           sub.ID(),                                      // Set topic to this subscription name to indicate DLQ issue
					Data:            msg.Data,                                      // Just a dump of the whole original payload
					DLQReason:       fmt.Sprintf("Error loading batch: %v", errLb), // Set new error reason
					PubsubTimestamp: msg.PublishTime,
					PubsubID:        msg.ID,
				}); errBa != nil {
					msg.Nack()
					return
				}
			}
			msg.Ack()
		})
		if err != nil {
			logger.Errorf("Failed to receive messages from subscription %s: %v", sub.ID(), err)
		}
	}
}

// Handler is the production-ready DLQ retry processor.
var Handler = HandlerWithDeps(HandlerDeps{
	Connector:       connect.GetConnections,
	ParseAttributes: pubsubdata.ParseAttributes,
	GetBatch:        bqbatch.GetBatch,
})
