package dlqBqBatch

import (
	"context"
	"encoding/json"
	"errors"
	"testing"
	"time"

	"cloud.google.com/go/pubsub"
	"github.com/stretchr/testify/assert"
	"synapse-its.com/shared/bqbatch"
	"synapse-its.com/shared/connect"
	"synapse-its.com/shared/mocks"
)

func TestHandlerWithDeps(t *testing.T) {
	type testCase struct {
		name             string
		getBatchErr      error
		receiveErr       error
		messages         []*pubsub.Message
		batcherAddErr    error
		batcherLoadErr   error
		expectAddCount   int
		expectLoadCount  int
		expectRetryCount int
	}

	tests := []testCase{
		{
			name:        "GetBatchError",
			getBatchErr: errors.New("batch error"),
			// <PERSON><PERSON> should return early without processing messages
			expectAddCount:  0,
			expectLoadCount: 0,
		},
		{
			name:            "ReceiveError",
			getBatchErr:     nil,
			receiveErr:      errors.New("receive error"),
			messages:        nil, // subscription.Receive returns error first
			expectAddCount:  0,
			expectLoadCount: 0,
		},
		{
			name:        "MalformedJSON_AddSuccess",
			getBatchErr: nil,
			receiveErr:  nil,
			messages: []*pubsub.Message{
				{
					Data:        []byte("not a valid json"),
					ID:          "msg1",
					PublishTime: time.Now(),
				},
			},
			batcherAddErr:   nil,
			expectAddCount:  1,
			expectLoadCount: 0,
		},
		{
			name:        "MalformedJSON_AddError",
			getBatchErr: nil,
			receiveErr:  nil,
			messages: []*pubsub.Message{
				{
					Data:        []byte("invalid_json"),
					ID:          "msg2",
					PublishTime: time.Now(),
				},
			},
			batcherAddErr:   errors.New("add error"),
			expectAddCount:  1,
			expectLoadCount: 0,
		},
		{
			name:        "ValidJSON_LoadSuccess",
			getBatchErr: nil,
			receiveErr:  nil,
			messages: func() []*pubsub.Message {
				failed := bqbatch.FailedBatch{
					Table:      "table1",
					Rows:       []map[string]interface{}{{}, {}},
					RetryCount: 0,
				}
				data, _ := json.Marshal(failed)
				return []*pubsub.Message{
					{
						Data:        data,
						ID:          "msg3",
						PublishTime: time.Now(),
					},
				}
			}(),
			batcherLoadErr:   nil,
			expectAddCount:   0,
			expectLoadCount:  1,
			expectRetryCount: 0,
		},
		{
			name:        "ValidJSON_LoadError_AddSuccess",
			getBatchErr: nil,
			receiveErr:  nil,
			messages: func() []*pubsub.Message {
				failed := bqbatch.FailedBatch{
					Table:      "table2",
					Rows:       []map[string]interface{}{{}, {}, {}},
					RetryCount: 0,
				}
				data, _ := json.Marshal(failed)
				return []*pubsub.Message{
					{
						Data:        data,
						ID:          "msg4",
						PublishTime: time.Now(),
					},
				}
			}(),
			batcherLoadErr:   errors.New("load error"),
			batcherAddErr:    nil,
			expectAddCount:   1,
			expectLoadCount:  1,
			expectRetryCount: 0,
		},
		{
			name:        "ValidJSON_LoadError_AddError",
			getBatchErr: nil,
			receiveErr:  nil,
			messages: func() []*pubsub.Message {
				failed := bqbatch.FailedBatch{
					Table:      "table3",
					Rows:       []map[string]interface{}{{}, {}, {}},
					RetryCount: 0,
				}
				data, _ := json.Marshal(failed)
				return []*pubsub.Message{
					{
						Data:        data,
						ID:          "msg5",
						PublishTime: time.Now(),
					},
				}
			}(),
			batcherLoadErr:   errors.New("load error"),
			batcherAddErr:    errors.New("add error"),
			expectAddCount:   1,
			expectLoadCount:  1,
			expectRetryCount: 0,
		},
	}

	for _, tc := range tests {
		tc := tc // capture range variable
		t.Run(tc.name, func(t *testing.T) {
			ctx := context.Background()
			subName := "test-sub-" + tc.name

			// Prepare FakeBatcher
			var addCount, loadCount int
			var lastProcessed bqbatch.FailedBatch

			fb := &mocks.FakeBatcher{
				AddFn: func(row interface{}) error {
					addCount++
					return tc.batcherAddErr
				},
				LoadBatchFn: func(failed bqbatch.FailedBatch) error {
					loadCount++
					lastProcessed = failed
					return tc.batcherLoadErr
				},
			}

			// Create a basic fake subscription for all test cases
			conns := mocks.FakeConns()
			client := conns.Pubsub.(*mocks.FakePubsubClient)
			client.ReceiveError = tc.receiveErr

			// Create a topic and subscription to avoid nil pointer dereference
			topic := client.Topic(subName)
			fakeSub, _ := client.CreateSubscription(ctx, subName, connect.SubscriptionConfig{Topic: topic})

			// Publish test messages to the topic if any
			if len(tc.messages) > 0 {
				for _, m := range tc.messages {
					topic.Publish(ctx, m)
				}
			}

			// Build deps
			deps := HandlerDeps{
				GetBatch: func(ctx context.Context) (bqbatch.Batcher, error) {
					if tc.getBatchErr != nil {
						return nil, tc.getBatchErr
					}
					return fb, nil
				},
				Connector:       nil, // not used in HandlerWithDeps
				ParseAttributes: nil, // not used in current handler
			}

			// Always call the handler to test the GetBatch error path
			handler := HandlerWithDeps(deps)
			handler(ctx, fakeSub)

			// Check counts
			assert.Equal(t, tc.expectAddCount, addCount, "unexpected Add call count")
			assert.Equal(t, tc.expectLoadCount, loadCount, "unexpected LoadBatch call count")

			// If expecting a retry, verify RetryCount increment
			if tc.expectRetryCount >= 0 && loadCount > 0 {
				assert.Equal(t, tc.expectRetryCount, lastProcessed.RetryCount, "unexpected RetryCount on processed batch")
			}
		})
	}
}
