package subscriptions

import (
	"time"

	"cloud.google.com/go/pubsub"
	dlqBqBatch "synapse-its.com/etl/processors/handlers/dlq/batch"
	dlqPubsub "synapse-its.com/etl/processors/handlers/dlq/messages"
	"synapse-its.com/etl/processors/handlers/gateway/faultLogs"
	"synapse-its.com/etl/processors/handlers/gateway/faultNotification"
	"synapse-its.com/etl/processors/handlers/gateway/gatewayLog"
	"synapse-its.com/etl/processors/handlers/gateway/macAddress"
	"synapse-its.com/etl/processors/handlers/gateway/monitorName"
	"synapse-its.com/etl/processors/handlers/gateway/perfStats"
	"synapse-its.com/etl/processors/handlers/gateway/rmsData"
	"synapse-its.com/etl/processors/handlers/gateway/rmsEngine"
	"synapse-its.com/etl/processors/handlers/notifications"
	"synapse-its.com/etl/processors/handlers/raw"
	"synapse-its.com/etl/processors/handlers/synapse/purgeExpired"
	"synapse-its.com/shared/pubsubdata"
)

// DefaultReceiveSettings for most subscriptions
func DefaultReceiveSettings() *pubsub.ReceiveSettings {
	return &pubsub.ReceiveSettings{
		MaxExtension:           5 * time.Minute,
		MaxExtensionPeriod:     1 * time.Minute,
		MinExtensionPeriod:     10 * time.Second, // Don't spam RPCs
		MaxOutstandingMessages: 1000,             // 1000 messages per subscription, most subscriptions will not need this many
		MaxOutstandingBytes:    1 << 30,          // 1GB
		NumGoroutines:          1,                // Possible issues with too many goroutines: https://github.com/googleapis/google-cloud-go/wiki/Fine-Tuning-PubSub-Receive-Performance
	}
}

// HighThroughputReceiveSettings for high throughput subscriptions
func HighThroughputReceiveSettings() *pubsub.ReceiveSettings {
	return &pubsub.ReceiveSettings{
		MaxExtension:           5 * time.Minute,
		MaxExtensionPeriod:     1 * time.Minute,
		MinExtensionPeriod:     10 * time.Second, // Don't spam RPCs
		MaxOutstandingMessages: 1000,             // 1000 messages per subscription
		MaxOutstandingBytes:    1 << 30,          // 1GB
		NumGoroutines:          5,                // 10 goroutines for the high throughput subscriptions
	}
}

func DefaultSubscriptions() []Subscription {
	return []Subscription{
		// Message processing handlers
		{Handler: rmsData.Handler, Name: pubsubdata.SubscriptionETLProcessingGatewayRMSData, ReceiveSettings: HighThroughputReceiveSettings()}, // High throughput due to much higher message volume
		{Handler: gatewayLog.Handler, Name: pubsubdata.SubscriptionETLProcessingGatewayGatewayLog, ReceiveSettings: DefaultReceiveSettings()},
		{Handler: faultNotification.Handler, Name: pubsubdata.SubscriptionETLProcessingGatewayFaultNotification, ReceiveSettings: DefaultReceiveSettings()},
		{Handler: rmsEngine.Handler, Name: pubsubdata.SubscriptionETLProcessingGatewayRMSEngine, ReceiveSettings: DefaultReceiveSettings()},
		{Handler: monitorName.Handler, Name: pubsubdata.SubscriptionETLProcessingGatewayMonitorName, ReceiveSettings: DefaultReceiveSettings()},
		{Handler: macAddress.Handler, Name: pubsubdata.SubscriptionETLProcessingGatewayMacAddress, ReceiveSettings: DefaultReceiveSettings()},
		{Handler: faultLogs.Handler, Name: pubsubdata.SubscriptionETLProcessingGatewayFaultLogs, ReceiveSettings: DefaultReceiveSettings()},
		{Handler: perfStats.Handler, Name: pubsubdata.SubscriptionETLProcessingGatewayPerfStats, ReceiveSettings: DefaultReceiveSettings()},
		{Handler: notifications.Handler, Name: pubsubdata.SubscriptionETLProcessingNotificationAlerts, ReceiveSettings: DefaultReceiveSettings()},
		{Handler: purgeExpired.Handler, Name: pubsubdata.SubscriptionETLProcessingSynapsePurgeExpired, ReceiveSettings: DefaultReceiveSettings()},
		// Raw handlers
		{Handler: raw.Handler, Name: pubsubdata.SubscriptionETLRawGatewayRMSData, ReceiveSettings: HighThroughputReceiveSettings()}, // High throughput due to much higher message volume
		{Handler: raw.Handler, Name: pubsubdata.SubscriptionETLRawGatewayGatewayLog, ReceiveSettings: DefaultReceiveSettings()},
		{Handler: raw.Handler, Name: pubsubdata.SubscriptionETLRawGatewayFaultNotification, ReceiveSettings: DefaultReceiveSettings()},
		{Handler: raw.Handler, Name: pubsubdata.SubscriptionETLRawGatewayRMSEngine, ReceiveSettings: DefaultReceiveSettings()},
		{Handler: raw.Handler, Name: pubsubdata.SubscriptionETLRawGatewayMonitorName, ReceiveSettings: DefaultReceiveSettings()},
		{Handler: raw.Handler, Name: pubsubdata.SubscriptionETLRawGatewayMacAddress, ReceiveSettings: DefaultReceiveSettings()},
		{Handler: raw.Handler, Name: pubsubdata.SubscriptionETLRawGatewayFaultLogs, ReceiveSettings: DefaultReceiveSettings()},
		{Handler: raw.Handler, Name: pubsubdata.SubscriptionETLRawGatewayPerfStats, ReceiveSettings: DefaultReceiveSettings()},
		{Handler: raw.Handler, Name: pubsubdata.SubscriptionETLRawNotificationAlerts, ReceiveSettings: DefaultReceiveSettings()},
		{Handler: raw.Handler, Name: pubsubdata.SubscriptionETLRawSynapsePurgeExpired, ReceiveSettings: DefaultReceiveSettings()},
		// DLQ handlers
		{Handler: dlqPubsub.Handler, Name: pubsubdata.SubscriptionETLProcessingDLQMessages, ReceiveSettings: DefaultReceiveSettings()},
		{Handler: dlqBqBatch.Handler, Name: pubsubdata.SubscriptionETLProcessingDLQBQBatch, ReceiveSettings: DefaultReceiveSettings()},
	}
}
