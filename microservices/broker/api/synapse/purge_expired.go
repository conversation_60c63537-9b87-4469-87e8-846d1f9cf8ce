package synapse

import (
	"context"
	"net/http"

	"cloud.google.com/go/pubsub"
	"synapse-its.com/shared/api/response"
	"synapse-its.com/shared/connect"
	"synapse-its.com/shared/logger"
	"synapse-its.com/shared/pubsubdata"
)

const topicName = pubsubdata.TopicBrokerSynapsePurgeExpired

// purgeExpiredDeps is the dependencies for the purge expired handler
type purgeExpiredDeps struct {
	getConnections func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error)
}

// newPurgeExpiredHandler creates a new purge expired handler
func newPurgeExpiredHandler(deps purgeExpiredDeps) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		ctx := r.Context()
		conns, err := deps.getConnections(ctx)
		if err != nil {
			logger.Errorf("Error getting connections: %v", err)
			response.CreateInternalErrorResponse(w)
			return
		}

		// Build pubsub message
		msg := &pubsub.Message{Attributes: map[string]string{
			"topic": topicName,
		}}

		// Publish to pubsub topic
		topic := conns.Pubsub.Topic(topicName)
		result := topic.Publish(ctx, msg)
		id, err := result.Get(ctx)
		if err != nil {
			logger.Errorf("Error publishing to pubsub topic %s: %v", topicName, err)
			response.CreateInternalErrorResponse(w)
			return
		}

		logger.Debugf("Published message ID %s to topic %s for org %s", id, topicName, "synapse")
		response.CreateSuccessResponse(msg, w)
	}
}
