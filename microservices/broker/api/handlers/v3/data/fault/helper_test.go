package fault

import (
	"encoding/json"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	ediHelper "synapse-its.com/shared/devices/edi/helper"
	"synapse-its.com/shared/schemas"
)

// TestConvertSchemasToFault verifies that ConvertSchemasToFault produces the
// expected UnifiedLogData given the sample inputs.
func TestConvertSchemasToFault(t *testing.T) {
	t.Parallel()
	got := ConvertSchemasTo16leip(
		SampleMessageTime,
		SampleSchemaName,
		SampleDeviceID,
		SampleDeviceDetail,
		SampleMonitorReset,
		SamplePrevFail,
		SampleConfig,
		SampleAcEvent,
		SampleFaultSeq,
	)

	// 1) Core metadata
	assert.True(t, got.MessageTime.Equal(SampleMessageTime), "MessageTime")
	assert.Equal(t, SampleDeviceID, got.DeviceID, "DeviceID")
	assert.Equal(t, SampleSchemaName, got.Schema, "Schema")

	// 2) DeviceDetail
	assert.Equal(t, SampleDeviceDetail, got.DeviceDetail, "DeviceDetail")

	// 3) LogMonitorReset
	require.Len(t, got.LogMonitorReset.Record, len(SampleMonitorReset.Records))
	for i, rec := range got.LogMonitorReset.Record {
		want := SampleMonitorReset.Records[i]
		assert.True(t, rec.DateTime.Equal(want.EventTimestamp), "LogMonitorReset[%d].DateTime", i)
		assert.Equal(t, want.ResetType, rec.ResetType, "LogMonitorReset[%d].ResetType", i)
	}

	// 4) LogPreviousFail (first record)
	require.Len(t, got.LogPreviousFail.Record, len(SamplePrevFail.Records))
	pr := got.LogPreviousFail.Record[0]
	sr := SamplePrevFail.Records[0]
	assert.True(t, pr.DateTime.Equal(sr.DateTime), "PrevFail.DateTime")
	assert.Equal(t, sr.Fault, pr.Fault, "PrevFail.Fault")
	assert.Equal(t, sr.ACLine, pr.ACLine, "PrevFail.ACLine")
	wantLs := ediHelper.EtlTranslators["LsFlashBit"].String(sr.LsFlashBit)
	assert.Equal(t, wantLs, pr.LsFlashBit, "PrevFail.LsFlashBit")

	// 5) LogConfigurationChange (first record permissives)
	require.Len(t, got.LogConfigurationChange.Record, len(SampleConfig.Record))
	cc := got.LogConfigurationChange.Record[0]
	sc := SampleConfig.Record[0]
	assert.Equal(t, sc.Ch01Permissives, cc.Ch1Permissives, "Ch1Permissives")
	assert.Equal(t, sc.Ch04Permissives, cc.Ch4Permissives, "Ch4Permissives")

	// 6) AC branch
	require.Len(t, got.LogAcLineEvent.Record, len(SampleAcEvent.Record))
	assert.Equal(t, "AC", got.LogAcLineEvent.VoltageType, "VoltageType(AC)")

	// 7) DC branch
	dcEvent := SampleAcEvent
	dcEvent.VoltageType = 2
	gotDC := ConvertSchemasTo16leip(
		SampleMessageTime,
		SampleSchemaName,
		SampleDeviceID,
		SampleDeviceDetail,
		SampleMonitorReset,
		SamplePrevFail,
		SampleConfig,
		dcEvent,
		SampleFaultSeq,
	)
	assert.Equal(t, "DC", gotDC.LogAcLineEvent.VoltageType, "VoltageType(DC)")

	// 8) FaultSignalSequence (first buffer)
	require.Len(t, got.LogFaultSignalSequence.Record, len(SampleFaultSeq.Records))

	fs := got.LogFaultSignalSequence.Record[0]
	// Now SampleFaultSeq.Records[0] is a LogFaultSignalSequenceRecords.
	// Its .Records[0] is the underlying TraceBuffer:
	inner := SampleFaultSeq.Records[0].Records[0]

	assert.Equal(t, int64(inner.AcVoltage), fs.Buffers[0].AcVoltage, "Buffer[0].AcVoltage")
}

func TestReplaceNullsInJSON(t *testing.T) {
	t.Parallel()
	input := []byte(`{"a":null,"b":1,"c":{"d":null},"e":[null,2]}`)
	gotJSON, err := ReplaceNullsInJSON(input)
	require.NoError(t, err, "ReplaceNullsInJSON should not error")

	var out map[string]interface{}
	require.NoError(t, json.Unmarshal(gotJSON, &out), "Unmarshal cleaned JSON")

	// a -> []
	a, ok := out["a"].([]interface{})
	assert.True(t, ok, "out['a'] should be []interface{}")
	assert.Len(t, a, 0, "out['a'] length")

	// b unchanged
	b, ok := out["b"].(float64)
	assert.True(t, ok, "out['b'] should be float64")
	assert.Equal(t, float64(1), b, "out['b'] value")

	// c.d -> []
	cObj, ok := out["c"].(map[string]interface{})
	require.True(t, ok, "out['c'] should be map[string]interface{}")
	d, ok := cObj["d"].([]interface{})
	assert.True(t, ok, "c['d'] should be []interface{}")
	assert.Len(t, d, 0, "c['d'] length")

	// e[0] -> [] and e[1] -> 2
	eArr, ok := out["e"].([]interface{})
	require.True(t, ok, "out['e'] should be []interface{}")
	assert.Len(t, eArr, 2, "out['e'] length")
	_, ok = eArr[0].([]interface{})
	assert.True(t, ok, "e[0] should be []interface{}")
	num, ok := eArr[1].(float64)
	assert.True(t, ok, "e[1] should be float64")
	assert.Equal(t, float64(2), num, "e[1] value")
}

func TestReplaceNullsInJSONInvalid(t *testing.T) {
	t.Parallel()
	input := []byte(`{invalid json}`)
	_, err := ReplaceNullsInJSON(input)
	assert.Error(t, err, "Expected error on invalid JSON")
}

var (
	// 1) Core metadata
	SampleMessageTime = time.Date(2025, 4, 16, 12, 24, 40, 528964700, time.UTC)
	SampleSchemaName  = "fault_mmu2_16leip.proto"
	SampleDeviceID    = "3fbe60189eef4bb6ab9c03663cc7b3ba"

	// 2) DeviceDetail
	SampleDeviceDetail = Logs_16leip_DeviceDetail{
		Model:                    "MMU2-16LEip",
		MonitorID:                8001,
		MonitorName:              "Westheimer Rd @ Sage Rd",
		FirmwareType:             "01",
		FirmwareVersion:          "7.5",
		MonitorCommVersion:       "3.8",
		RMSEngineFirmwareType:    "01",
		RMSEngineFirmwareVersion: "3.1",
		Logic24FirmwareType:      "",
		Logic24FirmwareVersion:   "",
		Manufacturer:             "EDI",
	}

	// 3) LogConfiguration – two Sample records
	SampleConfig = schemas.LogConfiguration{
		Record: []schemas.ConfigurationChangeLogRecord{
			{
				DateTime: time.Date(2024, 2, 10, 21, 22, 32, 0, time.UTC),

				Ch01Permissives: []string{"5", "6", "11", "13"},
				Ch02Permissives: []string{"5", "6", "9", "11", "13", "15"},
				Ch03Permissives: []string{"7", "8", "12", "16"},
				Ch04Permissives: []string{"7", "8", "10", "12", "14", "16"},
				Ch05Permissives: []string{"9", "13"},
				Ch06Permissives: []string{"9", "11", "13", "15"},
				Ch07Permissives: []string{"10", "14"},
				Ch08Permissives: []string{"10", "12", "14", "16"},
				Ch09Permissives: []string{"11", "13", "15"},
				Ch10Permissives: []string{"12", "14", "16"},
				Ch11Permissives: []string{"13", "15"},
				Ch12Permissives: []string{"14", "16"},
				Ch13Permissives: []string{"15"},
				Ch14Permissives: []string{"16"},
				Ch15Permissives: []string{},
				Ch16Permissives: []string{},

				MinimumYellowClearanceEnable:    []bool{true, true, true, true, true, true, true, true, false, false, false, false, true, true, true, true},
				MinimumYellowRedClearanceEnable: []bool{true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true},
				FieldCheckEnableRed:             []bool{true, true, true, true, true, false, false, false, false, false, false, false, false, false, false, false},
				GreenYellowDualEnable:           []bool{false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false},
				RedFailEnable:                   []bool{true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true},

				MinimumFlashTime:     "6 seconds",
				CvmLatchEnable:       false,
				X24VLatchEnable:      false,
				X24VoltInhibit:       true,
				Port1Disable:         true,
				TypeMode:             "16",
				RecurrentPulse:       true,
				WatchdogEnableSwitch: false,
				WalkEnableTs1:        false,
				LogCvmFaults:         true,
				ProgramCardMemory:    false,
				LEDGuardThresholds:   false,
				ForceType16Mode:      false,
				Type12WithSdlcMode:   false,

				FlashingYellowArrows:  []string{"Mode A, Channel Pairs: <none>"},
				FyaRedAndYellowEnable: "Channels: <none>",
				FyaRedAndGreenDisable: "",

				FyaYellowTrapDetection: false,
				FyaFlashRateDetection:  false,
				CheckValue:             "5594",
				ChangeSource:           "Front Panel 'Set Default' Entry",
			},
			{
				DateTime: time.Date(2024, 1, 17, 6, 30, 9, 0, time.UTC),

				Ch01Permissives: []string{},
				Ch02Permissives: []string{"6", "13", "15"},
				Ch03Permissives: []string{},
				Ch04Permissives: []string{"8", "9", "10", "14", "16"},
				Ch05Permissives: []string{},
				Ch06Permissives: []string{"13", "15"},
				Ch07Permissives: []string{},
				Ch08Permissives: []string{"9", "10", "14", "16"},
				Ch09Permissives: []string{"10", "11", "12", "14", "16"},
				Ch10Permissives: []string{"11", "12", "14", "16"},
				Ch11Permissives: []string{"12"},
				Ch12Permissives: []string{},
				Ch13Permissives: []string{},
				Ch14Permissives: []string{},
				Ch15Permissives: []string{},
				Ch16Permissives: []string{},

				MinimumYellowClearanceEnable:    []bool{false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false},
				MinimumYellowRedClearanceEnable: []bool{false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false},
				FieldCheckEnableRed:             []bool{false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false},
				GreenYellowDualEnable:           []bool{false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false},
				RedFailEnable:                   []bool{false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false},

				MinimumFlashTime:     "",
				CvmLatchEnable:       false,
				X24VLatchEnable:      false,
				X24VoltInhibit:       false,
				Port1Disable:         false,
				TypeMode:             "",
				RecurrentPulse:       false,
				WatchdogEnableSwitch: false,
				WalkEnableTs1:        false,
				LogCvmFaults:         false,
				ProgramCardMemory:    false,
				LEDGuardThresholds:   false,
				ForceType16Mode:      false,
				Type12WithSdlcMode:   false,

				FlashingYellowArrows:  []string{},
				FyaRedAndYellowEnable: "",
				FyaRedAndGreenDisable: "",

				FyaYellowTrapDetection: false,
				FyaFlashRateDetection:  false,
				CheckValue:             "",
				ChangeSource:           "",
			},
		},
		DeviceModel: "MMU2-16LEip",
		RawMessage:  nil,
	}

	// 4) LogMonitorReset – two entries
	SampleMonitorReset = schemas.LogMonitorReset{
		Records: []schemas.LogMonitorResetRecord{
			{EventTimestamp: time.Date(2024, 3, 16, 3, 11, 5, 0, time.UTC), ResetType: "MONITOR NON-LATCHED FAULT RESET EVENT #"},
			{EventTimestamp: time.Date(2024, 2, 26, 20, 39, 32, 0, time.UTC), ResetType: "MONITOR MANUAL RESET EVENT #"},
		},
		DeviceModel: "MMU2-16LEip",
		RawMessage:  nil,
	}

	// 5) LogPreviousFail – two entries
	SamplePrevFail = schemas.LogPreviousFail{
		Records: []schemas.LogPreviousFailRecord{
			{
				DateTime:                time.Date(2024, 3, 12, 12, 13, 3, 0, time.UTC),
				Fault:                   "Port 1 Fault",
				ACLine:                  "121 Vrms @ 60Hz",
				RedEnable:               "Active (122 Vrms)",
				MCCoilEE:                "",
				SpecialFunction1:        "",
				SpecialFunction2:        "",
				WDTMonitor:              "",
				T24VDCInput:             "",
				Temperature:             96,
				LsFlashBit:              false,
				FaultStatus:             []bool{},
				ChannelGreenStatus:      []bool{false, true, false, false, false, true, false, false, false, false, false, false, false, false, false, false},
				ChannelYellowStatus:     []bool{false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false},
				ChannelRedStatus:        []bool{true, false, true, true, true, false, true, true, true, true, true, true, true, true, true, true},
				ChannelWalkStatus:       []bool{},
				NextConflictingChannels: []bool{},
			},
			{
				DateTime:                time.Date(2024, 2, 10, 20, 52, 47, 0, time.UTC),
				Fault:                   "Dual Indication Fault",
				ACLine:                  "117 Vrms @ 60Hz",
				RedEnable:               "Active (118 Vrms)",
				Temperature:             80,
				LsFlashBit:              false,
				FaultStatus:             []bool{false, false, false, false, true, false, false, false, false, false, true, false, false, false, false, false},
				ChannelGreenStatus:      []bool{true, false, false, false, true, false, false, false, false, false, false, false, false, false, false, false},
				ChannelYellowStatus:     []bool{false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false},
				ChannelRedStatus:        []bool{false, true, true, true, false, true, true, true, true, true, true, true, true, true, true, true},
				ChannelWalkStatus:       []bool{},
				NextConflictingChannels: []bool{},
			},
		},
		DeviceModel: "MMU2-16LEip",
		RawMessage:  nil,
	}

	// 6) LogACLineEvent – two entries
	SampleAcEvent = schemas.LogACLineEvent{
		Record: []schemas.LogACLineEventRecord{
			{EventType: "AC Power Up", DateTime: time.Date(2024, 3, 16, 3, 10, 53, 0, time.UTC), LineVoltageRms: 122, LineFrequencyHz: 0},
			{EventType: "Power Down", DateTime: time.Date(2024, 3, 12, 12, 13, 28, 0, time.UTC), LineVoltageRms: 0, LineFrequencyHz: 0},
		},
		DeviceModel: "MMU2-16LEip",
		VoltageType: 1,
		RawMessage:  nil,
	}

	// 7) LogFaultSignalSequence – two “outer” records, each with a single TraceBuffer
	SampleFaultSeq = schemas.LogFaultSignalSequence{
		Records: []schemas.LogFaultSignalSequenceRecords{
			{
				TraceRawBytes: nil,            // or whatever byte makes sense; tests don’t exercise this
				FaultType:     "Port 1 Fault", // must match the parent fault type
				Records: []schemas.TraceBuffer{
					{
						BufferRawBytes: nil,
						Timestamp:      0,
						Reds:           []bool{true, false, true, true, true, false, true, true, true, true, true, true, true, true, true, true},
						Yellows:        []bool{false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false},
						Greens:         []bool{false, true, false, false, false, true, false, false, false, false, false, false, false, false, false, false},
						Walks:          []bool{},
						EE_SF_RE:       false,
						AcVoltage:      121,
					},
				},
			},
			{
				TraceRawBytes: nil,
				FaultType:     "Port 1 Fault",
				Records: []schemas.TraceBuffer{
					{
						BufferRawBytes: nil,
						Timestamp:      435,
						Reds:           []bool{true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true},
						Yellows:        []bool{false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false},
						Greens:         []bool{false, true, false, false, false, true, false, false, false, false, false, false, false, false, false, false},
						Walks:          []bool{},
						EE_SF_RE:       false,
						AcVoltage:      121,
					},
				},
			},
		},
		DeviceModel: "MMU2-16LEip",
		RawMessage:  nil,
	}
)

// TestConvertVS tests the convertVS function in helper.
func TestConvertVS_EmptyInput(t *testing.T) {
	t.Parallel()
	// Given a nil slice of schemas.VirtualSetting
	var src []schemas.VirtualSetting

	// When converting
	dst := convertVS(src)

	// Then result should be a zero-length slice
	require.Len(t, dst, 0, "convertVS(nil) should return an empty slice")
}

func TestConvertVS_PopulatedInput(t *testing.T) {
	t.Parallel()
	// Given a populated slice of schemas.VirtualSetting
	src := []schemas.VirtualSetting{
		{Color: "red", Enabled: true, SourceChannel: 1, SourceColor: "blue"},
		{Color: "green", Enabled: false, SourceChannel: 2, SourceColor: "yellow"},
	}

	// Expected output slice of helper.VirtualSetting
	want := []Logs_16leip_VirtualSetting{
		{Color: "red", Enabled: true, SourceChannel: 1, SourceColor: "blue"},
		{Color: "green", Enabled: false, SourceChannel: 2, SourceColor: "yellow"},
	}

	// When converting
	got := convertVS(src)

	// Then
	require.Equal(t, want, got, "convertVS should map schemas.VirtualSetting to helper.VirtualSetting correctly")
}

func TestParseRequest_Valid(t *testing.T) {
	t.Parallel()
	req := httptest.NewRequest("GET", "http://example.com/?deviceid=42", nil)
	id, err := parseRequest(req)
	require.NoError(t, err)
	assert.Equal(t, int64(42), id)
}

func TestParseRequest_Missing(t *testing.T) {
	t.Parallel()
	req := httptest.NewRequest("GET", "http://example.com/", nil)
	_, err := parseRequest(req)
	require.Error(t, err)
	assert.Contains(t, err.Error(), "invalid query url")
}

func TestParseRequest_Invalid(t *testing.T) {
	t.Parallel()
	req := httptest.NewRequest("GET", "http://example.com/?deviceid=notanint", nil)
	_, err := parseRequest(req)
	require.Error(t, err)
	assert.Contains(t, err.Error(), ErrConvertQueryParam.Error())
}

// Tests for getSchema (handler.go) ------------------------------------------
func TestGetSchema_Ecl2010(t *testing.T) {
	t.Parallel()
	schema := getSchema(int64(ediHelper.Ecl2010))
	assert.Equal(t, "fault_ecl2010_fplus.proto", schema)
}

func TestGetSchema_CMU2212(t *testing.T) {
	t.Parallel()
	schema := getSchema(int64(ediHelper.CMUip2212_hv))
	assert.Equal(t, "fault_cmu2212_base.proto", schema)
}

func TestGetSchema_Mmu16le(t *testing.T) {
	t.Parallel()
	schema := getSchema(int64(ediHelper.Mmu16le))
	assert.Equal(t, "fault_mmu2_16leip.proto", schema)
}

func TestGetSchema_Default(t *testing.T) {
	t.Parallel()
	// unknown model should default to MMU16 proto
	schema := getSchema(int64(9999))
	assert.Equal(t, "fault_mmu2_16leip.proto", schema)
}

func TestConvertSchemasToECL2010FPlus_Minimal(t *testing.T) {
	t.Parallel()
	// Use the same sample logs defined above, and a dummy detail
	detail := &Logs_ECL2010_FPlus_LogData_DeviceDetail{
		Model:                    "TEST",
		MonitorId:                1234,
		MonitorName:              "FooBar",
		FirmwareType:             "FT",
		FirmwareVersion:          "FV",
		MonitorCommVersion:       "MCV",
		RmsEngineFirmwareVersion: "EV",
		Manufacturer:             "ACME",
	}

	out := ConvertSchemasToECL2010FPlus(
		SampleMessageTime,
		SampleSchemaName,
		SampleDeviceID,
		detail,
		SampleMonitorReset,
		SamplePrevFail,
		SampleConfig,
		SampleAcEvent,
		SampleFaultSeq,
	)

	// Basic metadata
	assert.NotNil(t, out, "LogData should not be nil")
	assert.True(t, out.MessageTime.Equal(SampleMessageTime), "MessageTime")
	assert.Equal(t, SampleSchemaName, out.Schema, "Schema")
	assert.Equal(t, SampleDeviceID, out.DeviceId, "DeviceId")
	assert.Equal(t, detail, out.DeviceDetail, "DeviceDetail")

	// Slices from Sample* variables
	assert.Len(t, out.LogMonitorReset.Record, len(SampleMonitorReset.Records), "LogMonitorReset count")
	assert.Len(t, out.LogPreviousFail.Record, len(SamplePrevFail.Records), "LogPreviousFail count")
	assert.Len(t, out.LogConfigurationChange.Record, len(SampleConfig.Record), "LogConfiguration count")
	assert.Len(t, out.LogAcLineEvent.Events, len(SampleAcEvent.Record), "ACLineEvent count")
	assert.Len(t, out.LogFaultSignalSequence.Record, len(SampleFaultSeq.Records), "FaultSignalSequence count")
}

func TestConvertSchemasToCMU2212Base_Minimal(t *testing.T) {
	t.Parallel()

	detail := &Logs_CMU2212_Base_LogData_DeviceDetail{
		Model:                    "TEST",
		MonitorId:                4321,
		MonitorName:              "BazQux",
		FirmwareType:             "FT2",
		FirmwareVersion:          "FV2",
		MonitorCommVersion:       "MCV2",
		RmsEngineFirmwareType:    "ET2",
		RmsEngineFirmwareVersion: "EV2",
		Manufacturer:             "ACME2",
	}

	out := ConvertSchemasToCMU2212Base(
		SampleMessageTime,
		SampleSchemaName,
		SampleDeviceID,
		detail,
		SampleMonitorReset,
		SamplePrevFail,
		SampleConfig,
		SampleAcEvent,
		SampleFaultSeq,
	)

	// Basic metadata
	require.NotNil(t, out, "LogData should not be nil")
	assert.True(t, out.MessageTime.Equal(SampleMessageTime), "MessageTime")
	assert.Equal(t, SampleSchemaName, out.Schema, "Schema")
	assert.Equal(t, SampleDeviceID, out.DeviceId, "DeviceId")
	assert.Equal(t, detail, out.DeviceDetail, "DeviceDetail")

	// The CMU2212_Base implementation always yields an empty FaultSignalSequence
	assert.Len(t, out.LogFaultSignalSequence.Record, 0, "FaultSignalSequence should be empty")
}

// func TestConvertSchemasToCMU2212Base_Full(t *testing.T) {
// 	t.Parallel()

// 	allLogs := schemas.AllLogs{
// 		LogMonitorReset:  getProcessLogMonitorReset(),
// 		LogPreviousFail:  getProcessLogPreviousFail(),
// 		LogConfiguration: getProcessLogConfiguration(),
// 		LogACLineEvent:   getProcessLogACLineEvent(),
// 		// LogFaultSignalSequence: getProcessLogFaultSignalSequence(),
// 		LogFaultSignalSequence: schemas.LogFaultSignalSequence{},
// 	}

// 	deviceDetail := &Logs_CMU2212_Base_LogData_DeviceDetail{
// 		Model:                    allLogs.FaultLogs.DeviceModel,
// 		MonitorId:                55,
// 		MonitorName:              "test name",
// 		FirmwareType:             "1",
// 		FirmwareVersion:          "2",
// 		MonitorCommVersion:       "3",
// 		RmsEngineFirmwareType:    "4",
// 		RmsEngineFirmwareVersion: "5",
// 		Logic24FirmwareType:      "6",
// 		Logic24FirmwareVersion:   "7",
// 		Manufacturer:             "EDI",
// 	}

// 	out := ConvertSchemasToCMU2212Base(
// 		allLogs.FaultLogs.PubsubTimestamp,
// 		"testschema",
// 		"426b0919812b490d8b48b0ac788b9712",
// 		deviceDetail,
// 		allLogs.LogMonitorReset,
// 		allLogs.LogPreviousFail,
// 		allLogs.LogConfiguration,
// 		allLogs.LogACLineEvent,
// 		allLogs.LogFaultSignalSequence,
// 	)

// 	logger.Errorf("testing: %v", out)
// 	// Basic metadata
// 	// require.NotNil(t, out.LogData, "LogData should not be nil")
// 	// assert.True(t, out.LogData.MessageTime.Equal(SampleMessageTime), "MessageTime")
// 	// assert.Equal(t, SampleSchemaName, out.LogData.Schema, "Schema")
// 	// assert.Equal(t, SampleDeviceID, out.LogData.DeviceId, "DeviceId")
// 	// assert.Equal(t, deviceDetail, out.LogData.DeviceDetail, "DeviceDetail")

// 	// // The CMU2212_Base implementation always yields an empty FaultSignalSequence
// 	// assert.Len(t, out.LogData.LogFaultSignalSequence.Record, 0, "FaultSignalSequence should be empty")
// }

// func getProcessLogFaultSignalSequence() (*ediHelper.HeaderRecord, *ediHelper.FaultSignalSequenceRecords) {
// 	const rawB64 = "kjkzARkAAAUPGgR3DwAAAAAAAIgAAAAAAAAA93kS8ncPAAAAAAAAiAAAAAAAAAD3eRLofw8AAAAAAACIAAAAAAAAAPd6Ecb/DwAAAAAAAAAAAAAAAAAA93oRvP8PAAAEAAAAAAAAAAAAAAD3eQ6muw8AAEQAAAAAAAAAAAAAAPd6Dpy7DwAARAAAAAQAAAAAAAAA93oKvrsPAAAAAAAARAAAAAAAAAD3egq0vw8AAAAAAABEAAAAAAAAAPd6CYj/DwAAAAAAAAAAAAAAAAAA93oJft8PAAAiAAAAAAAAAAAAAAD3egZo3Q8AACIAAAAAAAAAAAAAAPd5Bl7dDwAAAgAAACIAAAAAAAAA93oAKN0PAAAAAAAAIgAAAAAAAAD3egAe3QMAAAAAAAAiAAAAAAAAAPd5hw=="

// 	// Decode Base64 into bytes
// 	byteMsg, _ := base64.StdEncoding.DecodeString(rawB64)

// 	// Manually construct the HTTP header for the test
// 	header := &pubsubdata.HeaderDetails{
// 		Host:            "",
// 		UserAgent:       "Go-http-client/2.0",
// 		ContentLength:   "2452",
// 		ContentType:     "application/x-protobuf",
// 		GatewayDeviceID: "d5c29fc65d7dc9e0798f68e4f4b982853587acfb5686e981c517849973198e9f",
// 		MessageVersion:  "v1",
// 		MessageType:     "faultLogs",
// 		GatewayTimezone: "America/Chicago",
// 	}
// 	// Invoke the function under test
// 	records, hdr, _ := devices.ProcessLogFaultSignalSequence(header, byteMsg)
// 	return hdr, records
// }

// func getProcessLogConfiguration() schemas.LogConfiguration {
// 	const rawB64 = "lTkzARkAAAEgGAIAAKYAAAAjAACwAgAAAgAAKAAAIAAAoAAAIAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAD/DwAAAAAAAAAAAAAAAAAAAAAAAP8AAAD/AAAA/w8AAP8AAAD/DwAAAAAAAAAAAAAAAAAAAAAAABQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFP8PAAD/AAAA/w8AAAwAAAAAAAAAAAAAAAAA/w8AAAATkyZDCBMFJaCUCAkKCxgZAQMFBxESAgQGCBUW5w=="

// 	// Decode Base64 into bytes
// 	byteMsg, _ := base64.StdEncoding.DecodeString(rawB64)

// 	// Manually construct the HTTP header for the test
// 	header := &pubsubdata.HeaderDetails{
// 		Host:            "",
// 		UserAgent:       "Go-http-client/2.0",
// 		ContentLength:   "2452",
// 		ContentType:     "application/x-protobuf",
// 		GatewayDeviceID: "d5c29fc65d7dc9e0798f68e4f4b982853587acfb5686e981c517849973198e9f",
// 		MessageVersion:  "v1",
// 		MessageType:     "faultLogs",
// 		GatewayTimezone: "America/Chicago",
// 	}
// 	// Invoke the function under test
// 	parsedRecords, parsedHeader, _ := devices.ProcessLogConfiguration(header, byteMsg)
// 	return schemas.LogConfiguration{
// 		OrganizationIdentifier: "DEV5AC4410CB8F3C8E29CE9210E5913B7B3B872B2BC",
// 		SoftwareGatewayID:      "d5c29fc65d7dc9e0798f68e4f4b982853587acfb5686e981c517849973198e9f",
// 		TZ:                     "America/Chicago",
// 		Topic:                  "broker-gateway-faultLogs",
// 		PubsubTimestamp:        SampleMessageTime,
// 		PubsubID:               "15214615909021789",
// 		DeviceID:               "426b0919812b490d8b48b0ac788b9712",
// 		Header:                 parsedHeader.ToBigQuerySchema(),
// 		Record: func() []schemas.ConfigurationChangeLogRecord {
// 			records := make([]schemas.ConfigurationChangeLogRecord, len(parsedRecords.Record))
// 			for i, record := range parsedRecords.Record {
// 				records[i] = record.ToBigQuerySchema()
// 			}
// 			return records
// 		}(),
// 		DeviceModel: parsedRecords.DeviceModel,
// 		RawMessage:  parsedRecords.RawMessage,
// 		LogUUID:     "1234",
// 	}
// }

// func getProcessLogMonitorReset() schemas.LogMonitorReset {
// 	const rawB64 = "ijkzARkAAAY4KBIUBSUCUicSFAUlATInEhQFJQIxBwgUBSUCEQYIFAUlAkkFCBQFJQFO"

// 	// Decode Base64 into bytes
// 	byteMsg, _ := base64.StdEncoding.DecodeString(rawB64)

// 	// Manually construct the HTTP header for the test
// 	header := &pubsubdata.HeaderDetails{
// 		Host:            "",
// 		UserAgent:       "Go-http-client/2.0",
// 		ContentLength:   "2452",
// 		ContentType:     "application/x-protobuf",
// 		GatewayDeviceID: "d5c29fc65d7dc9e0798f68e4f4b982853587acfb5686e981c517849973198e9f",
// 		MessageVersion:  "v1",
// 		MessageType:     "faultLogs",
// 		GatewayTimezone: "America/Chicago",
// 	}
// 	// Invoke the function under test
// 	parsedRecords, parsedHeader, _ := devices.ProcessLogMonitorReset(header, byteMsg)

// 	return schemas.LogMonitorReset{
// 		OrganizationIdentifier: "DEV5AC4410CB8F3C8E29CE9210E5913B7B3B872B2BC",
// 		SoftwareGatewayID:      "d5c29fc65d7dc9e0798f68e4f4b982853587acfb5686e981c517849973198e9f",
// 		TZ:                     "America/Chicago",
// 		Topic:                  "broker-gateway-faultLogs",
// 		PubsubTimestamp:        SampleMessageTime,
// 		PubsubID:               "15214615909021789",
// 		DeviceID:               "426b0919812b490d8b48b0ac788b9712",
// 		Header:                 *parsedHeader.ToBigQuerySchema(),
// 		Records: func() []schemas.LogMonitorResetRecord {
// 			records := make([]schemas.LogMonitorResetRecord, len(parsedRecords.Records))
// 			for i, record := range parsedRecords.Records {
// 				records[i] = record.ToBigQuerySchema()
// 			}
// 			return records
// 		}(),
// 		DeviceModel: parsedRecords.DeviceModel,
// 		RawMessage:  parsedRecords.RawMessage,
// 		LogUUID:     "1234",
// 	}
// }

// func getProcessLogPreviousFail() schemas.LogPreviousFail {
// 	const rawB64 = "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"

// 	// Decode Base64 into bytes
// 	byteMsg, _ := base64.StdEncoding.DecodeString(rawB64)

// 	// Manually construct the HTTP header for the test
// 	header := &pubsubdata.HeaderDetails{
// 		Host:            "",
// 		UserAgent:       "Go-http-client/2.0",
// 		ContentLength:   "2452",
// 		ContentType:     "application/x-protobuf",
// 		GatewayDeviceID: "d5c29fc65d7dc9e0798f68e4f4b982853587acfb5686e981c517849973198e9f",
// 		MessageVersion:  "v1",
// 		MessageType:     "faultLogs",
// 		GatewayTimezone: "America/Chicago",
// 	}
// 	// Invoke the function under test
// 	parsedRecords, parsedHeader, _ := devices.ProcessLogPreviousFail(header, byteMsg)

// 	return schemas.LogPreviousFail{
// 		OrganizationIdentifier: "DEV5AC4410CB8F3C8E29CE9210E5913B7B3B872B2BC",
// 		SoftwareGatewayID:      "d5c29fc65d7dc9e0798f68e4f4b982853587acfb5686e981c517849973198e9f",
// 		TZ:                     "America/Chicago",
// 		Topic:                  "broker-gateway-faultLogs",
// 		PubsubTimestamp:        SampleMessageTime,
// 		PubsubID:               "15214615909021789",
// 		DeviceID:               "426b0919812b490d8b48b0ac788b9712",
// 		Header:                 *parsedHeader.ToBigQuerySchema(),
// 		Records: func() []schemas.LogPreviousFailRecord {
// 			records := make([]schemas.LogPreviousFailRecord, len(parsedRecords.Records))
// 			for i, record := range parsedRecords.Records {
// 				records[i] = record.ToBigQuerySchema()
// 			}
// 			return records
// 		}(),
// 		DeviceModel: parsedRecords.DeviceModel,
// 		RawMessage:  parsedRecords.RawMessage,
// 		LogUUID:     "1234",
// 	}
// }

// func getProcessLogACLineEvent() schemas.LogACLineEvent {
// 	const rawB64 = "iDkzARkAAAIFfDEHCBQFJZSqgQAlBwgUBSUAAL8="

// 	// Decode Base64 into bytes
// 	byteMsg, _ := base64.StdEncoding.DecodeString(rawB64)

// 	// Manually construct the HTTP header for the test
// 	header := &pubsubdata.HeaderDetails{
// 		Host:            "",
// 		UserAgent:       "Go-http-client/2.0",
// 		ContentLength:   "2452",
// 		ContentType:     "application/x-protobuf",
// 		GatewayDeviceID: "d5c29fc65d7dc9e0798f68e4f4b982853587acfb5686e981c517849973198e9f",
// 		MessageVersion:  "v1",
// 		MessageType:     "faultLogs",
// 		GatewayTimezone: "America/Chicago",
// 	}
// 	// Invoke the function under test
// 	parsedRecords, parsedHeader, _ := devices.ProcessLogACLineEvent(header, byteMsg)
// 	return schemas.LogACLineEvent{
// 		OrganizationIdentifier: "DEV5AC4410CB8F3C8E29CE9210E5913B7B3B872B2BC",
// 		SoftwareGatewayID:      "d5c29fc65d7dc9e0798f68e4f4b982853587acfb5686e981c517849973198e9f",
// 		TZ:                     "America/Chicago",
// 		Topic:                  "broker-gateway-faultLogs",
// 		PubsubTimestamp:        SampleMessageTime,
// 		PubsubID:               "15214615909021789",
// 		DeviceID:               "426b0919812b490d8b48b0ac788b9712",
// 		Header:                 *parsedHeader.ToBigQuerySchema(),
// 		Record: func() []schemas.LogACLineEventRecord {
// 			records := make([]schemas.LogACLineEventRecord, len(parsedRecords.Records))
// 			for i, record := range parsedRecords.Records {
// 				records[i] = record.ToBigQuerySchema()
// 			}
// 			return records
// 		}(),
// 		DeviceModel: parsedRecords.DeviceModel,
// 		RawMessage:  parsedRecords.RawMessage,
// 		LogUUID:     "1234",
// 	}
// }
