module synapse-its.com/shared/mocks

go 1.24.1

replace synapse-its.com/shared/api => /shared/api

replace synapse-its.com/shared/connect => /shared/connect

replace synapse-its.com/shared/logger => /shared/logger

replace synapse-its.com/shared/mocks => /shared/mocks

replace synapse-its.com/shared/schema_mgmt => /shared/schema_mgmt

replace synapse-its.com/shared/pubsubdata => /shared/pubsubdata

replace bitbucket.org/synapse-its/protobuf-schemas => /shared/protobuf-schemas

require (
	cloud.google.com/go/bigquery v1.68.0
	cloud.google.com/go/pubsub v1.49.0
	github.com/alicebob/miniredis/v2 v2.35.0
	github.com/redis/go-redis/v9 v9.7.1
	github.com/stretchr/testify v1.10.0
	google.golang.org/api v0.231.0
	synapse-its.com/shared/bqbatch v0.0.0-00010101000000-000000000000
	synapse-its.com/shared/connect v0.0.0-00010101000000-000000000000
	synapse-its.com/shared/logger v0.0.0-00010101000000-000000000000
)

require (
	cloud.google.com/go v0.121.0 // indirect
	cloud.google.com/go/auth v0.16.1 // indirect
	cloud.google.com/go/auth/oauth2adapt v0.2.8 // indirect
	cloud.google.com/go/compute/metadata v0.6.0 // indirect
	cloud.google.com/go/firestore v1.18.0 // indirect
	cloud.google.com/go/iam v1.5.2 // indirect
	cloud.google.com/go/longrunning v0.6.7 // indirect
	github.com/apache/arrow/go/v15 v15.0.2 // indirect
	github.com/cespare/xxhash/v2 v2.3.0 // indirect
	github.com/davecgh/go-spew v1.1.1 // indirect
	github.com/dgryski/go-rendezvous v0.0.0-20200823014737-9f7001d12a5f // indirect
	github.com/felixge/httpsnoop v1.0.4 // indirect
	github.com/go-logr/logr v1.4.2 // indirect
	github.com/go-logr/stdr v1.2.2 // indirect
	github.com/goccy/go-json v0.10.2 // indirect
	github.com/google/flatbuffers v23.5.26+incompatible // indirect
	github.com/google/s2a-go v0.1.9 // indirect
	github.com/google/uuid v1.6.0 // indirect
	github.com/googleapis/enterprise-certificate-proxy v0.3.6 // indirect
	github.com/googleapis/gax-go/v2 v2.14.1 // indirect
	github.com/klauspost/compress v1.16.7 // indirect
	github.com/klauspost/cpuid/v2 v2.2.5 // indirect
	github.com/lib/pq v1.10.9 // indirect
	github.com/pierrec/lz4/v4 v4.1.18 // indirect
	github.com/pmezard/go-difflib v1.0.0 // indirect
	github.com/stretchr/objx v0.5.2 // indirect
	github.com/yuin/gopher-lua v1.1.1 // indirect
	github.com/zeebo/xxh3 v1.0.2 // indirect
	go.opencensus.io v0.24.0 // indirect
	go.opentelemetry.io/auto/sdk v1.1.0 // indirect
	go.opentelemetry.io/contrib/instrumentation/google.golang.org/grpc/otelgrpc v0.60.0 // indirect
	go.opentelemetry.io/contrib/instrumentation/net/http/otelhttp v0.60.0 // indirect
	go.opentelemetry.io/otel v1.35.0 // indirect
	go.opentelemetry.io/otel/metric v1.35.0 // indirect
	go.opentelemetry.io/otel/trace v1.35.0 // indirect
	go.uber.org/multierr v1.10.0 // indirect
	go.uber.org/zap v1.27.0 // indirect
	golang.org/x/crypto v0.37.0 // indirect
	golang.org/x/exp v0.0.0-20240719175910-8a7402abbf56 // indirect
	golang.org/x/mod v0.23.0 // indirect
	golang.org/x/net v0.39.0 // indirect
	golang.org/x/oauth2 v0.29.0 // indirect
	golang.org/x/sync v0.14.0 // indirect
	golang.org/x/sys v0.32.0 // indirect
	golang.org/x/text v0.24.0 // indirect
	golang.org/x/time v0.11.0 // indirect
	golang.org/x/tools v0.30.0 // indirect
	golang.org/x/xerrors v0.0.0-20240903120638-7835f813f4da // indirect
	google.golang.org/genproto v0.0.0-20250303144028-a0af3efb3deb // indirect
	google.golang.org/genproto/googleapis/api v0.0.0-20250428153025-10db94c68c34 // indirect
	google.golang.org/genproto/googleapis/rpc v0.0.0-20250428153025-10db94c68c34 // indirect
	google.golang.org/grpc v1.72.0 // indirect
	google.golang.org/protobuf v1.36.6 // indirect
	gopkg.in/yaml.v3 v3.0.1 // indirect
	synapse-its.com/shared/api v0.0.0-00010101000000-000000000000 // indirect
	synapse-its.com/shared/pubsubdata v0.0.0-00010101000000-000000000000 // indirect
	synapse-its.com/shared/schemas v0.0.0-00010101000000-000000000000 // indirect
)

replace synapse-its.com/shared/devices => /shared/devices

replace synapse-its.com/shared/util => /shared/util

replace synapse-its.com/shared/healthz => /shared/healthz

replace synapse-its.com/shared/httplogger => /shared/httplogger

replace synapse-its.com/shared/bqbatch => /shared/bqbatch

replace synapse-its.com/shared/schemas => /shared/schemas

replace synapse-its.com/shared/rest => /shared/rest
