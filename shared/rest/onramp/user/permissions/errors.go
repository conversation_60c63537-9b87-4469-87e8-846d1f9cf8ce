package permissions

import "errors"

var (
	// HandlerWithDeps() errors
	ErrUserInfoRetrieve    = errors.New("unable to retrieve user info from request context")
	ErrConnectionsRetrieve = errors.New("unable to retrieve database connections")
	ErrUserPermissionsGet  = errors.New("unable to get user permissions")

	// getUserPermissions() errors
	ErrAuthorizerPermissionsGet = errors.New("unable to get permissions from authorizer")
	ErrPermissionsTransform     = errors.New("unable to transform permissions to API format")
)
