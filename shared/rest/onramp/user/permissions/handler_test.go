package permissions

import (
	"context"
	"errors"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/stretchr/testify/assert"
	"synapse-its.com/shared/api/authorizer"
	"synapse-its.com/shared/rest/onramp/session"
)

// Test_HandlerWithDeps tests the HandlerWithDeps function with all scenarios
func Test_HandlerWithDeps(t *testing.T) {
	tests := []struct {
		name               string
		setupDeps          func() HandlerDeps
		setupRequest       func() *http.Request
		expectedStatusCode int
		expectedBody       string
		wantErr            bool
	}{
		{
			name: "success_case",
			setupDeps: func() HandlerDeps {
				mockUserPermissions := &authorizer.UserPermissions{
					UserID: "550e8400-e29b-41d4-a716-446655440001",
					Permissions: []authorizer.Permission{
						{
							Scope:          "org",
							ScopeID:        "550e8400-e29b-41d4-a716-446655440010",
							OrganizationID: "550e8400-e29b-41d4-a716-446655440010",
							Permissions:    []string{"org_view_users", "org_manage_users"},
						},
					},
				}

				expectedResponse := &UserPermissionsResponse{
					UserID: "550e8400-e29b-41d4-a716-446655440001",
					Permissions: []PermissionScope{
						{
							Scope:          "org",
							ScopeID:        stringPtr("550e8400-e29b-41d4-a716-446655440010"),
							OrganizationID: stringPtr("550e8400-e29b-41d4-a716-446655440010"),
							Permissions:    []string{"org_view_users", "org_manage_users"},
						},
					},
				}

				return HandlerDeps{
					UserInfoFromSessionContext: func(ctx context.Context) (*authorizer.UserPermissions, bool) {
						return mockUserPermissions, true
					},
					TransformPermissionsToResponse: func(userPermissions *authorizer.UserPermissions) (*UserPermissionsResponse, error) {
						assert.Equal(t, "550e8400-e29b-41d4-a716-446655440001", userPermissions.UserID)
						return expectedResponse, nil
					},
				}
			},
			setupRequest: func() *http.Request {
				return httptest.NewRequest(http.MethodGet, "/api/v3/user/permissions", nil)
			},
			expectedStatusCode: http.StatusOK,
			wantErr:            false,
		},
		{
			name: "user_permissions_from_context_fails",
			setupDeps: func() HandlerDeps {
				return HandlerDeps{
					UserInfoFromSessionContext: func(ctx context.Context) (*authorizer.UserPermissions, bool) {
						return nil, false
					},
					TransformPermissionsToResponse: func(userPermissions *authorizer.UserPermissions) (*UserPermissionsResponse, error) {
						return nil, nil
					},
				}
			},
			setupRequest: func() *http.Request {
				return httptest.NewRequest(http.MethodGet, "/api/v3/user/permissions", nil)
			},
			expectedStatusCode: http.StatusInternalServerError,
			wantErr:            true,
		},
		{
			name: "transform_permissions_fails",
			setupDeps: func() HandlerDeps {
				return HandlerDeps{
					UserInfoFromSessionContext: func(ctx context.Context) (*authorizer.UserPermissions, bool) {
						return &authorizer.UserPermissions{UserID: "test-user"}, true
					},
					TransformPermissionsToResponse: func(userPermissions *authorizer.UserPermissions) (*UserPermissionsResponse, error) {
						return nil, errors.New("transform failed")
					},
				}
			},
			setupRequest: func() *http.Request {
				return httptest.NewRequest(http.MethodGet, "/api/v3/user/permissions", nil)
			},
			expectedStatusCode: http.StatusInternalServerError,
			wantErr:            true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Setup dependencies
			deps := tt.setupDeps()

			// Create handler
			handler := HandlerWithDeps(deps)

			// Setup request
			req := tt.setupRequest()
			w := httptest.NewRecorder()

			// Execute handler
			handler(w, req)

			// Assert response
			assert.Equal(t, tt.expectedStatusCode, w.Code)

			if tt.wantErr {
				assert.NotEqual(t, http.StatusOK, w.Code)
			} else {
				assert.Equal(t, http.StatusOK, w.Code)
			}
		})
	}
}

// Test_transformPermissionsToResponse tests the transformPermissionsToResponse function with all scenarios
func Test_transformPermissionsToResponse(t *testing.T) {
	tests := []struct {
		name             string
		userPermissions  *authorizer.UserPermissions
		expectedResponse *UserPermissionsResponse
		expectedError    error
		wantErr          bool
	}{
		{
			name: "success_with_org_permissions",
			userPermissions: &authorizer.UserPermissions{
				UserID: "550e8400-e29b-41d4-a716-446655440001",
				Permissions: []authorizer.Permission{
					{
						Scope:          "org",
						ScopeID:        "550e8400-e29b-41d4-a716-446655440010",
						OrganizationID: "550e8400-e29b-41d4-a716-446655440010",
						Permissions:    []string{"org_view_users", "org_manage_users"},
					},
				},
			},
			expectedResponse: &UserPermissionsResponse{
				UserID: "550e8400-e29b-41d4-a716-446655440001",
				Permissions: []PermissionScope{
					{
						Scope:          "org",
						ScopeID:        stringPtr("550e8400-e29b-41d4-a716-446655440010"),
						OrganizationID: stringPtr("550e8400-e29b-41d4-a716-446655440010"),
						Permissions:    []string{"org_view_users", "org_manage_users"},
					},
				},
			},
			wantErr: false,
		},
		{
			name: "success_with_device_group_permissions",
			userPermissions: &authorizer.UserPermissions{
				UserID: "550e8400-e29b-41d4-a716-446655440001",
				Permissions: []authorizer.Permission{
					{
						Scope:          "device_group",
						ScopeID:        "550e8400-e29b-41d4-a716-446655440020",
						OrganizationID: "550e8400-e29b-41d4-a716-446655440010",
						Permissions:    []string{"device_group_view_devices", "device_group_manage_devices"},
					},
				},
			},
			expectedResponse: &UserPermissionsResponse{
				UserID: "550e8400-e29b-41d4-a716-446655440001",
				Permissions: []PermissionScope{
					{
						Scope:          "device_group",
						ScopeID:        stringPtr("550e8400-e29b-41d4-a716-446655440020"),
						OrganizationID: stringPtr("550e8400-e29b-41d4-a716-446655440010"),
						Permissions:    []string{"device_group_view_devices", "device_group_manage_devices"},
					},
				},
			},
			wantErr: false,
		},
		{
			name: "success_with_global_scope_empty_ids",
			userPermissions: &authorizer.UserPermissions{
				UserID: "550e8400-e29b-41d4-a716-446655440001",
				Permissions: []authorizer.Permission{
					{
						Scope:          "global",
						ScopeID:        "",
						OrganizationID: "",
						Permissions:    []string{"global_admin"},
					},
				},
			},
			expectedResponse: &UserPermissionsResponse{
				UserID: "550e8400-e29b-41d4-a716-446655440001",
				Permissions: []PermissionScope{
					{
						Scope:          "global",
						ScopeID:        nil,
						OrganizationID: nil,
						Permissions:    []string{"global_admin"},
					},
				},
			},
			wantErr: false,
		},
		{
			name: "success_with_multiple_permissions",
			userPermissions: &authorizer.UserPermissions{
				UserID: "550e8400-e29b-41d4-a716-446655440001",
				Permissions: []authorizer.Permission{
					{
						Scope:          "org",
						ScopeID:        "550e8400-e29b-41d4-a716-446655440010",
						OrganizationID: "550e8400-e29b-41d4-a716-446655440010",
						Permissions:    []string{"org_view_users"},
					},
					{
						Scope:          "device_group",
						ScopeID:        "550e8400-e29b-41d4-a716-446655440020",
						OrganizationID: "550e8400-e29b-41d4-a716-446655440010",
						Permissions:    []string{"device_group_view_devices"},
					},
				},
			},
			expectedResponse: &UserPermissionsResponse{
				UserID: "550e8400-e29b-41d4-a716-446655440001",
				Permissions: []PermissionScope{
					{
						Scope:          "org",
						ScopeID:        stringPtr("550e8400-e29b-41d4-a716-446655440010"),
						OrganizationID: stringPtr("550e8400-e29b-41d4-a716-446655440010"),
						Permissions:    []string{"org_view_users"},
					},
					{
						Scope:          "device_group",
						ScopeID:        stringPtr("550e8400-e29b-41d4-a716-446655440020"),
						OrganizationID: stringPtr("550e8400-e29b-41d4-a716-446655440010"),
						Permissions:    []string{"device_group_view_devices"},
					},
				},
			},
			wantErr: false,
		},
		{
			name: "success_with_empty_permissions",
			userPermissions: &authorizer.UserPermissions{
				UserID:      "550e8400-e29b-41d4-a716-446655440001",
				Permissions: []authorizer.Permission{},
			},
			expectedResponse: &UserPermissionsResponse{
				UserID:      "550e8400-e29b-41d4-a716-446655440001",
				Permissions: []PermissionScope{},
			},
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Execute function
			result, err := transformPermissionsToResponse(tt.userPermissions)

			// Assert results
			if tt.wantErr {
				assert.Error(t, err)
				assert.Equal(t, tt.expectedError, err)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.expectedResponse, result)
			}
		})
	}
}

// Test_Handler tests the production Handler
func Test_Handler(t *testing.T) {
	t.Parallel()

	// Create a test request
	req := httptest.NewRequest(http.MethodGet, "/api/v3/user/permissions", nil)
	w := httptest.NewRecorder()

	// Execute handler
	Handler(w, req)

	// Since we don't have session data in the test context, it should return an error
	assert.Equal(t, http.StatusInternalServerError, w.Code)
}

// Test_userInfoFromSessionContext tests the userInfoFromSessionContext function
func Test_userInfoFromSessionContext(t *testing.T) {
	tests := []struct {
		name           string
		setupContext   func() context.Context
		expectedResult *authorizer.UserPermissions
		expectedOk     bool
	}{
		{
			name: "session_with_user_permissions",
			setupContext: func() context.Context {
				userPerms := &authorizer.UserPermissions{
					UserID: "test-user-id",
					Permissions: []authorizer.Permission{
						{
							Scope:          "org",
							ScopeID:        "org-id",
							OrganizationID: "org-id",
							Permissions:    []string{"org_view"},
						},
					},
				}
				sessionData := &session.SessionData{
					UserID:          "test-user-id",
					UserPermissions: userPerms,
					Data:            make(map[string]any),
				}
				return context.WithValue(context.Background(), "session", sessionData)
			},
			expectedResult: &authorizer.UserPermissions{
				UserID: "test-user-id",
				Permissions: []authorizer.Permission{
					{
						Scope:          "org",
						ScopeID:        "org-id",
						OrganizationID: "org-id",
						Permissions:    []string{"org_view"},
					},
				},
			},
			expectedOk: true,
		},
		{
			name: "session_without_user_permissions",
			setupContext: func() context.Context {
				sessionData := &session.SessionData{
					UserID:          "test-user-id",
					UserPermissions: nil,
					Data:            make(map[string]any),
				}
				return context.WithValue(context.Background(), "session", sessionData)
			},
			expectedResult: &authorizer.UserPermissions{
				UserID:      "test-user-id",
				Permissions: []authorizer.Permission{},
			},
			expectedOk: true,
		},
		{
			name: "no_session_in_context",
			setupContext: func() context.Context {
				return context.Background()
			},
			expectedResult: nil,
			expectedOk:     false,
		},
		{
			name: "session_with_wrong_type",
			setupContext: func() context.Context {
				return context.WithValue(context.Background(), "session", "not-a-session")
			},
			expectedResult: nil,
			expectedOk:     false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Setup context
			ctx := tt.setupContext()

			// Execute function
			result, ok := UserInfoFromSessionContext(ctx)

			// Assert results
			if tt.expectedOk {
				assert.True(t, ok)
				assert.Equal(t, tt.expectedResult, result)
			} else {
				assert.False(t, ok)
				assert.Nil(t, result)
			}
		})
	}
}

func stringPtr(s string) *string {
	return &s
}
