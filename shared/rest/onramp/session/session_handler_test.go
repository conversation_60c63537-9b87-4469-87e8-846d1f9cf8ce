package session

import (
	"testing"
)

func TestSetAndGetSession(t *testing.T) {
	store := NewSessionStore()
	sessionID := "test-session"
	expectedData := &SessionData{
		UserID: "user123",
		Data:   map[string]any{"foo": "bar"},
	}

	store.SetSession(sessionID, expectedData)

	retrievedData, ok := store.GetSession(sessionID)
	if !ok {
		t.Fatalf("Expected sessionID %s to be found", sessionID)
	}

	if retrievedData.UserID != expectedData.UserID {
		t.<PERSON><PERSON>("Expected UserID %s, got %s", expectedData.UserID, retrievedData.UserID)
	}

	if retrievedData.Data["foo"] != "bar" {
		t.<PERSON><PERSON>("Expected session data foo=bar, got foo=%v", retrievedData.Data["foo"])
	}
}

func TestClearSession(t *testing.T) {
	store := NewSessionStore()
	sessionID := "session-to-clear"

	store.SetSession(sessionID, &SessionData{
		UserID: "to-delete",
	})

	store.ClearSession(sessionID)

	_, ok := store.GetSession(sessionID)
	if ok {
		t.<PERSON><PERSON><PERSON>("Expected sessionID %s to be cleared", sessionID)
	}
}

func TestGetSessionNotFound(t *testing.T) {
	store := NewSessionStore()
	_, ok := store.GetSession("nonexistent")
	if ok {
		t.Errorf("Expected GetSession to return false for nonexistent session")
	}
}

func TestSetSession(t *testing.T) {
	store := NewSessionStore()

	sessionID := "set-session"
	sessionData := &SessionData{
		UserID: "set-user",
		Data:   map[string]any{"lang": "go"},
	}

	store.SetSession(sessionID, sessionData)

	internalData, exists := store.store[sessionID]
	if !exists {
		t.Fatalf("Expected sessionID %s to exist in store", sessionID)
	}

	if internalData.UserID != "set-user" {
		t.Errorf("Expected UserID 'set-user', got %s", internalData.UserID)
	}

	if val, ok := internalData.Data["lang"]; !ok || val != "go" {
		t.Errorf("Expected session data lang=go, got %v", val)
	}
}
