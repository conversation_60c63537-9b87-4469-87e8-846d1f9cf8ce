package session

import (
	"sync"

	"golang.org/x/oauth2"
	"synapse-its.com/shared/api/authorizer"
)

// SessionData holds information for a user session.
type SessionData struct {
	UserID          string
	OAuthToken      *oauth2.Token
	UserPermissions *authorizer.UserPermissions
	Data            map[string]any
}

// SetRequest represents a request to set a key-value pair in a session.
type SetRequest struct {
	Key   string      `json:"key"`
	Value interface{} `json:"value"`
}

// SessionStore defines the interface for session management.
type SessionStore interface {
	GetSession(sessionID string) (*SessionData, bool)
	SetSession(sessionID string, data *SessionData)
	ClearSession(sessionID string)
}

// InMemorySessionStore is a thread-safe in-memory implementation of SessionStore.
type InMemorySessionStore struct {
	mutex sync.RWMutex
	store map[string]*SessionData
}

// NewInMemorySessionStore creates a new in-memory session store.
func NewSessionStore() *InMemorySessionStore {
	return &InMemorySessionStore{
		store: make(map[string]*SessionData),
	}
}

func (s *InMemorySessionStore) GetSession(sessionID string) (*SessionData, bool) {
	s.mutex.RLock()
	defer s.mutex.RUnlock()
	data, ok := s.store[sessionID]
	return data, ok
}

func (s *InMemorySessionStore) SetSession(sessionID string, data *SessionData) {
	s.mutex.Lock()
	defer s.mutex.Unlock()
	s.store[sessionID] = data
}

func (s *InMemorySessionStore) ClearSession(sessionID string) {
	s.mutex.Lock()
	defer s.mutex.Unlock()
	delete(s.store, sessionID)
}
