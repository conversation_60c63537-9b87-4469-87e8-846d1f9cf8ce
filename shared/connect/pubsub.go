package connect

import (
	"context"
	"fmt"
	"os"
	"time"

	"cloud.google.com/go/pubsub"
	"google.golang.org/api/option"
	"google.golang.org/api/option/internaloption"
	"google.golang.org/grpc"
	"google.golang.org/grpc/credentials/insecure"

	"synapse-its.com/shared/logger"
)

var (
	pubsubNewClient = pubsub.NewClient
	timeSleepPubSub = time.Sleep
)

var PubSub = func(ctx context.Context) (PsClient, error) {
	projectID := os.Getenv("GCP_PROJECT_ID")
	if projectID == "" {
		projectID = "test-project"
	}
	opts := []option.ClientOption{}
	endpoint := os.Getenv("PUBSUB_EMULATOR_HOST")
	if endpoint != "" {
		// https://github.com/googleapis/google-cloud-go/blob/bfb6c05baed4e9a22a84d600700e5954cc5fc55a/pubsub/pubsub.go#L155
		opts = []option.ClientOption{
			option.WithEndpoint(endpoint),
			option.WithGRPCDialOption(grpc.WithTransportCredentials(insecure.NewCredentials())),
			option.WithoutAuthentication(),
			option.WithTelemetryDisabled(),
			internaloption.SkipDialSettingsValidation(),
		}
	}

	var client *pubsub.Client
	var err error
	backoff := time.Second
	maxRetries := 5

	for i := range maxRetries {
		client, err = pubsubNewClient(ctx, projectID, opts...)
		if err == nil {
			break
		}
		logger.Infof("Attempt %d/%d: Failed to create PubSub client, retrying in %s: %v", i+1, maxRetries, backoff, err)
		timeSleepPubSub(backoff)
		backoff *= 2
	}

	if err != nil {
		return nil, fmt.Errorf("failed to create PubSub client after %d attempts: %v", maxRetries, err)
	}
	return WrapPubsubClient(client), nil
}

// ——— Interfaces ——————————————————————————————————————

// PsClient abstracts *pubsub.Client
type PsClient interface {
	Topic(name string) PsTopic
	Subscription(name string) PsSubscription
	CreateTopic(ctx context.Context, name string) (PsTopic, error)
	CreateSubscription(ctx context.Context, name string, cfg SubscriptionConfig) (PsSubscription, error)
	Close() error
}

// PsTopic abstracts *pubsub.Topic
type PsTopic interface {
	// Publish returns a result you can call Get() on
	Publish(ctx context.Context, msg *pubsub.Message) PsPublishResult
	// Exists lets you check for existence
	Exists(ctx context.Context) (bool, error)
}

// PsPublishResult abstracts *pubsub.PublishResult
type PsPublishResult interface {
	Get(ctx context.Context) (string, error)
}

// PsSubscription abstracts *pubsub.Subscription
type PsSubscription interface {
	Receive(ctx context.Context, f func(context.Context, *pubsub.Message)) error
	Exists(ctx context.Context) (bool, error)
	SetReceiveSettings(settings *pubsub.ReceiveSettings)
	ID() string
	Close() error
}

// ——— Pubsub implementations ————————————————————————————

type PubsubClient struct{ c *pubsub.Client }

func WrapPubsubClient(c *pubsub.Client) PsClient { return &PubsubClient{c} }

func (r *PubsubClient) Topic(name string) PsTopic {
	return &PubsubTopic{r.c.Topic(name)}
}

func (r *PubsubClient) Subscription(name string) PsSubscription {
	return &PubsubSubscription{r.c.Subscription(name)}
}

func (r *PubsubClient) Close() error { return r.c.Close() }

type PubsubTopic struct{ t *pubsub.Topic }

func (r *PubsubTopic) Publish(ctx context.Context, msg *pubsub.Message) PsPublishResult {
	return &PubsubPublishResult{r.t.Publish(ctx, msg)}
}

func (r *PubsubTopic) Exists(ctx context.Context) (bool, error) {
	return r.t.Exists(ctx)
}

type PubsubPublishResult struct{ r *pubsub.PublishResult }

func (r *PubsubPublishResult) Get(ctx context.Context) (string, error) {
	return r.r.Get(ctx)
}

type PubsubSubscription struct{ s *pubsub.Subscription }

func (r *PubsubSubscription) Receive(ctx context.Context, f func(context.Context, *pubsub.Message)) error {
	return r.s.Receive(ctx, f)
}

func (r *PubsubSubscription) SetReceiveSettings(settings *pubsub.ReceiveSettings) {
	if settings != nil {
		r.s.ReceiveSettings = *settings
	}
}

func (r *PubsubSubscription) ID() string {
	return r.s.ID()
}

func (r *PubsubSubscription) Exists(ctx context.Context) (bool, error) {
	return r.s.Exists(ctx)
}

func (r *PubsubSubscription) Close() error { return nil }

type SubscriptionConfig struct {
	Topic       PsTopic
	AckDeadline time.Duration
}

func (c *PubsubClient) CreateTopic(ctx context.Context, name string) (PsTopic, error) {
	t, err := c.c.CreateTopic(ctx, name)
	if err != nil {
		return nil, err
	}
	return &PubsubTopic{t}, nil
}

func (c *PubsubClient) CreateSubscription(ctx context.Context, name string, cfg SubscriptionConfig) (PsSubscription, error) {
	// unwrap our PsTopic back to *pubsub.Topic
	realTopic := cfg.Topic.(*PubsubTopic).t
	sub, err := c.c.CreateSubscription(ctx, name, pubsub.SubscriptionConfig{
		Topic:       realTopic,
		AckDeadline: cfg.AckDeadline,
		// … any other fields …
	})
	if err != nil {
		return nil, err
	}
	return &PubsubSubscription{sub}, nil
}

var IsValidPubSubTopic = func(topicName string, ctx context.Context, client PsClient) (bool, error) {
	topic := client.Topic(topicName)
	validTopic, err := topic.Exists(ctx)
	if err != nil {
		logger.Errorf("Error topic %q does not exists: %v", topicName, err)
		return false, err
	}
	return validTopic, err
}
