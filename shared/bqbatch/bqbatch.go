package bqbatch

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"os"
	"reflect"
	"strconv"
	"sync"
	"time"

	"cloud.google.com/go/bigquery"
	"cloud.google.com/go/pubsub"

	"synapse-its.com/shared/connect"
	"synapse-its.com/shared/logger"
	"synapse-its.com/shared/mocks/bqexecutor"
	"synapse-its.com/shared/pubsubdata"
	"synapse-its.com/shared/schemas"
)

// This type is used as a key in which to store the batch in the context.  It
// exists simply to make the linters happy.
type contextBatchKey string

// This constant is the identifier used in the context to store the connection information.
const BatchKey contextBatchKey = "bqbatch"

// PubSub topic name for DLQ backup incase bigquery is down.
const PubsubDLQTopic string = pubsubdata.TopicDLQBQBatch

var (
	// The maximum number of rows that a queue should hold before it flushes.
	MaxRows = 5000
	// The maximum time that a queue should wait before it flushes.
	MaxWait = 10 * time.Second
	// The number of retries allowed if load fails.
	MaxAttempts = 5

	// BigQuery load job size limit (16MB)
	MaxBigQueryBatchSize = 16 * 1024 * 1024 // 16MB in bytes
	// PubSub message size limit (10MB)
	MaxPubSubMessageSize = 10 * 1024 * 1024 // 10MB in bytes

	// Default metrics flush interval
	defaultMetricsFlushInterval = 30 * time.Second
	// Default concurrency limit
	defaultMaxConcurrency = 3

	// Allow overriding time.Sleep for testing
	timeSleep = time.Sleep
)

// Batcher is the public interface.  You Register types, Add rows, then Shutdown.
type Batcher interface {
	// Register ties a Go struct type (T or *T) to a BigQuery table name and config.
	Register(rowExample any, table string, cfg QueueConfig) error
	// Add enqueues a row; it'll dispatch to whichever queue was registered for that type.
	Add(row any) error
	// Immediately attempt to load a previously failed batch.
	LoadBatch(batch FailedBatch) error
	// Shutdown flushes all queues and stops accepting new rows.
	Shutdown() error
}

// batcher is the concrete implementation of Batcher.
type batcher struct {
	exec        connect.BigQueryExecutorInterface
	psClient    connect.PsClient
	cfgs        map[string]QueueConfig
	queues      map[string]*queue
	typeToTable map[reflect.Type]string
	mu          sync.Mutex
	shutdown    bool
}

// queue represents a per-table buffer and its worker.
type queue struct {
	table    string
	cfg      QueueConfig
	schema   bigquery.Schema
	exec     connect.BigQueryExecutorInterface
	psClient connect.PsClient
	batcher  *batcher // Reference to parent batcher for metrics

	// Used to stop the worker.
	ctrlCtx context.Context
	cancel  context.CancelFunc

	// Used for BQ calls.
	loadCtx context.Context

	rows []bigquery.ValueSaver
	mu   sync.Mutex
	wg   sync.WaitGroup

	// Performance metrics accumulator and mutex
	metricsMu sync.Mutex
	metrics   schemas.BatchPerformanceStats
}

func init() {
	// This is a in-production safety check.  If it fails, the process will be
	// terminated.  It is not a test, but a sanity check.

	// Validate that all the default table/struct pairings are valid.
	for _, q := range queues {
		if q.table == "" {
			logger.Fatalf("Default Batcher failed validation: table name is empty")
		}
		if _, _, err := getValidType(q.rowExample); err != nil {
			logger.Fatalf("Default Batcher failed validation: %v", err)
		}
	}

	// Validate that the batcher initializes correctly.
	_, err := newDefault(&bqexecutor.FakeBigQueryExecutor{
		Ctx: context.Background(),
	}, nil)
	if err != nil {
		logger.Fatalf("Default Batcher failed validation: %v", ErrDefaultBatcher)
	}

	// Override the MaxRows and MaxWait values if they are overridden in the
	// environment variables.
	if maxRows := os.Getenv("BQBATCH_MAX_ROWS"); maxRows != "" {
		maxRowsInt, err := strconv.Atoi(maxRows)
		if err == nil && maxRowsInt > 0 {
			MaxRows = maxRowsInt
		}
	}
	if maxWait := os.Getenv("BQBATCH_MAX_WAIT"); maxWait != "" {
		maxWaitInt, err := strconv.Atoi(maxWait)
		if err == nil && maxWaitInt > 0 {
			MaxWait = time.Duration(maxWaitInt) * time.Second
		}
	}
	if maxAttempts := os.Getenv("BQBATCH_MAX_ATTEMPTS"); maxAttempts != "" {
		maxAttemptsInt, err := strconv.Atoi(maxAttempts)
		if err == nil && maxAttemptsInt > 0 {
			MaxAttempts = maxAttemptsInt
		}
	}
	if maxBQSize := os.Getenv("BQBATCH_MAX_BQ_SIZE"); maxBQSize != "" {
		maxBQSizeInt, err := strconv.Atoi(maxBQSize)
		if err == nil && maxBQSizeInt > 0 {
			MaxBigQueryBatchSize = maxBQSizeInt
		}
	}
	if maxPSSize := os.Getenv("BQBATCH_MAX_PS_SIZE"); maxPSSize != "" {
		maxPSSizeInt, err := strconv.Atoi(maxPSSize)
		if err == nil && maxPSSizeInt > 0 {
			MaxPubSubMessageSize = maxPSSizeInt
		}
	}
	if metricsFlushInterval := os.Getenv("BQBATCH_METRICS_FLUSH_INTERVAL"); metricsFlushInterval != "" {
		metricsFlushIntervalInt, err := strconv.Atoi(metricsFlushInterval)
		if err == nil && metricsFlushIntervalInt > 0 {
			defaultMetricsFlushInterval = time.Duration(metricsFlushIntervalInt) * time.Second
		}
	}
	if maxConcurrency := os.Getenv("BQBATCH_MAX_CONCURRENCY"); maxConcurrency != "" {
		maxConcurrencyInt, err := strconv.Atoi(maxConcurrency)
		if err == nil && maxConcurrencyInt > 0 {
			defaultMaxConcurrency = maxConcurrencyInt
		}
	}
}

// Helper function that assembles a batch handler for use in the data-core
// microservices.  This function registers the default table/struct pairings.
func NewDefault(exec connect.BigQueryExecutorInterface, psclient connect.PsClient) Batcher {
	// To be clear, ignoring the error is fine here because the unit test has
	// already verified that all the default table/struct pairings are valid.
	// This is just a convenience function for the data-core microservices.
	// It is also verified in the init() function.
	b, _ := newDefault(exec, psclient)
	return b
}

// This is a list of the default table/struct pairings.  It is validated in a
// unit test that all pairings are valid.
var queues = []struct {
	rowExample any
	table      string
}{
	{schemas.EdiRawMessages{}, schemas.T_EdiRawMessages},
	{schemas.DlqMessages{}, schemas.T_DlqMessages},
	{schemas.RmsEngine{}, schemas.T_RmsEngine},
	{schemas.MonitorName{}, schemas.T_MonitorName},
	{schemas.MacAddress{}, schemas.T_MacAddress},
	{schemas.RmsData{}, schemas.T_RmsData},
	{schemas.FaultNotification{}, schemas.T_FaultNotification},
	{schemas.GatewayPerformanceStatistics{}, schemas.T_GatewayPerformanceStatistics},
	{schemas.GatewayLogMessage{}, schemas.T_GatewayLogMessage},
	{schemas.FaultLogs{}, schemas.T_FaultLogs},
	{schemas.LogMonitorReset{}, schemas.T_LogMonitorReset},
	{schemas.LogPreviousFail{}, schemas.T_logPreviousFail},
	{schemas.LogACLineEvent{}, schemas.T_logACLineEvent},
	{schemas.LogFaultSignalSequence{}, schemas.T_logFaultSignalSequence},
	{schemas.LogConfiguration{}, schemas.T_logConfiguration},
	{schemas.NotificationMessages{}, schemas.T_NotificationMessages},
	{schemas.BatchPerformanceStats{}, schemas.T_BatchPerformanceStats},
}

// Helper function (used by the unit tests) that ensures that all registered
// table/struct pairings are valid.  This exists so that the public
// NewDefault() function can be used cleanly.
func newDefault(exec connect.BigQueryExecutorInterface, psClient connect.PsClient) (Batcher, error) {
	b := New(exec, psClient)

	defaultConfig := QueueConfig{
		MaxSize:              1000,
		FlushInterval:        1 * time.Second,
		MetricsFlushInterval: defaultMetricsFlushInterval,
		MaxConcurrency:       defaultMaxConcurrency,
	}

	for _, q := range queues {
		// Errors are ignored because a unit test has already verified that all
		// the default table/struct pairings are valid and also because the module
		// will fail on init() if any of the default table/struct pairings are
		// invalid.
		_ = b.Register(q.rowExample, q.table, defaultConfig)
	}

	return b, nil
}

// New returns an empty batcher; you must Register() at least once.
func New(exec connect.BigQueryExecutorInterface, psClient connect.PsClient) Batcher {
	return &batcher{
		exec:        exec,
		psClient:    psClient,
		cfgs:        make(map[string]QueueConfig),
		queues:      make(map[string]*queue),
		typeToTable: make(map[reflect.Type]string),
	}
}

// Helper function that validates that the provided type is valid for use in
// the batcher.
func getValidType(t any) (reflect.Type, bigquery.Schema, error) {
	// Figure out the underlying struct type.
	tType := reflect.TypeOf(t)
	if tType == nil {
		return nil, nil, ErrNotStruct
	}
	if tType.Kind() == reflect.Ptr {
		tType = tType.Elem()
	}
	if tType.Kind() != reflect.Struct {
		return nil, nil, ErrNotStruct
	}

	// Verify that the schema can be inferred.
	schema, err := bigquery.InferSchema(t)
	if err != nil {
		return nil, nil, err
	}
	return tType, schema, nil
}

// Register ties a Go struct type (T or *T) to a BigQuery table name and config.
// This is a required step before you can Add() rows.
// The rowExample parameter is used to infer the schema of the table.
func (b *batcher) Register(rowExample any, table string, cfg QueueConfig) error {
	t, schema, err := getValidType(rowExample)
	if err != nil {
		return err
	}

	b.mu.Lock()
	defer b.mu.Unlock()

	if b.shutdown {
		return ErrBatcherIsShutDown
	}
	if _, exists := b.typeToTable[t]; exists {
		return fmt.Errorf("type %s already registered", t)
	}

	// Remember the mapping.
	b.typeToTable[t] = table
	b.cfgs[table] = cfg

	// Create the queue.
	b.queues[table] = newQueue(b.exec, b.psClient, table, cfg, schema, b)
	return nil
}

func (b *batcher) Add(row any) error {
	// Figure out the underlying struct type.
	t := reflect.TypeOf(row)
	if t == nil {
		return ErrNotStruct
	}
	if t.Kind() == reflect.Ptr {
		t = t.Elem()
	}
	if t.Kind() != reflect.Struct {
		return ErrNotStruct
	}

	// Lock the *batcher*.
	b.mu.Lock()

	// Validate the state of the batcher and whether the type is registered.
	if b.shutdown {
		b.mu.Unlock()
		return ErrBatcherIsShutDown
	}
	table, ok := b.typeToTable[t]
	if !ok {
		b.mu.Unlock()
		return ErrUnknownType
	}
	q, ok := b.queues[table]
	b.mu.Unlock()
	if !ok {
		return fmt.Errorf("%w (%s)", ErrUnknownType, table)
	}
	// Batch lock is no longer held.

	// Lock the *queue*.
	q.mu.Lock()

	// Append the row to the queue.
	q.rows = append(q.rows, &bigquery.StructSaver{
		Struct: row,
		Schema: q.schema,
	})

	// Check if we need to flush.
	sz := len(q.rows)
	q.mu.Unlock()
	if sz >= q.cfg.MaxSize {
		q.flushAsync()
	}

	return nil
}

// Shutdown signals all queue workers to flush and stop.
func (b *batcher) Shutdown() error {
	b.mu.Lock()
	if b.shutdown {
		b.mu.Unlock()
		return ErrBatcherIsShutDown
	}
	b.shutdown = true
	// Kick off all the workers to flush.
	for _, q := range b.queues {
		q.cancel()
	}
	b.mu.Unlock()

	// Wait for them all.
	for _, q := range b.queues {
		q.wg.Wait()
	}

	return nil
}

// newQueue initializes a queue and starts its worker.
func newQueue(exec connect.BigQueryExecutorInterface, psClient connect.PsClient, table string, cfg QueueConfig, schema bigquery.Schema, batcher *batcher) *queue {
	// ctrlCtx controls the worker's lifecycle
	ctrlCtx, cancel := context.WithCancel(exec.GetContext())
	// loadCtx is the "root" context for BQ operations (loader.Run / job.Wait)
	loadCtx := exec.GetContext()

	q := &queue{
		table:    table,
		cfg:      cfg,
		schema:   schema,
		exec:     exec,
		psClient: psClient,
		batcher:  batcher,
		ctrlCtx:  ctrlCtx,
		cancel:   cancel,
		loadCtx:  loadCtx,
		rows:     make([]bigquery.ValueSaver, 0, cfg.MaxSize),
		metrics: schemas.BatchPerformanceStats{
			Table: table,
		},
	}
	q.wg.Add(1)
	go q.worker()
	return q
}

// worker handles timed flushes and shutdown.
func (q *queue) worker() {
	defer q.wg.Done()
	ticker := time.NewTicker(q.cfg.FlushInterval)

	// Ensure metrics flush interval is positive
	metricsInterval := q.cfg.MetricsFlushInterval
	if metricsInterval <= 0 {
		metricsInterval = defaultMetricsFlushInterval
	}
	statTicker := time.NewTicker(metricsInterval)
	defer ticker.Stop()
	defer statTicker.Stop()

	for {
		select {
		case <-ticker.C:
			q.flushAsync()
		case <-statTicker.C:
			q.flushMetrics()
		case <-q.ctrlCtx.Done():
			q.flushSync()
			q.flushMetrics()
			return
		}
	}
}

// flushAsync triggers a non-blocking flush.
func (q *queue) flushAsync() {
	// copy rows under lock
	q.mu.Lock()
	if len(q.rows) == 0 {
		q.mu.Unlock()
		return
	}
	batch := make([]bigquery.ValueSaver, len(q.rows))
	copy(batch, q.rows)
	q.rows = q.rows[:0]
	q.mu.Unlock()
	// run load in goroutine
	go q.load(batch, 0)
}

// flushSync does a blocking flush (for shutdown).
func (q *queue) flushSync() {
	q.mu.Lock()
	if len(q.rows) == 0 {
		q.mu.Unlock()
		return
	}
	batch := make([]bigquery.ValueSaver, len(q.rows))
	copy(batch, q.rows)
	q.rows = q.rows[:0]
	q.mu.Unlock()
	q.load(batch, 0)
}

// preSerializeRows serializes each row once and returns PreSerializedRow slices
func preSerializeRows(rows []bigquery.ValueSaver) ([]PreSerializedRow, error) {
	var result []PreSerializedRow
	for _, r := range rows {
		rowMap, _, err := r.Save()
		if err != nil {
			return nil, err
		}
		jsonBytes, err := json.Marshal(rowMap)
		if err != nil {
			return nil, err
		}
		result = append(result, PreSerializedRow{
			ValueSaver: r,
			JSONBytes:  jsonBytes,
			Size:       len(jsonBytes) + 1, // +1 for newline in JSONL
			RowMap:     rowMap,
		})
	}
	return result, nil
}

// splitBatch splits a batch of pre-serialized rows into sub-batches that fit within size limits
func splitBatch(rows []PreSerializedRow, maxSize int) [][]PreSerializedRow {
	var batches [][]PreSerializedRow
	var currentBatch []PreSerializedRow
	var currentSize int
	for _, row := range rows {
		if currentSize+row.Size > maxSize && len(currentBatch) > 0 {
			batches = append(batches, currentBatch)
			currentBatch = []PreSerializedRow{row}
			currentSize = row.Size
		} else {
			currentBatch = append(currentBatch, row)
			currentSize += row.Size
		}
	}
	if len(currentBatch) > 0 {
		batches = append(batches, currentBatch)
	}
	return batches
}

// load performs the BigQuery batch load for the given rows.
func (q *queue) load(rows []bigquery.ValueSaver, retryCount int) {
	startTime := time.Now()
	originalRows := len(rows)

	if retryCount > MaxAttempts {
		logger.Error("Max retry attempts reached, dumping records to logs")
		logger.Error(rows)
		return
	}

	// Get the table name in the correct namespace
	tablename := connect.CombineTableNamespace(q.exec.GetConfig().Namespace, q.table)

	// Track processing time (pre-serialization and splitting)
	processingStart := time.Now()

	// Pre-serialize all rows
	preRows, err := preSerializeRows(rows)
	if err != nil {
		logger.Errorf("Failed to pre-serialize rows: %v", err)
		return
	}

	// Split the batch if it exceeds BigQuery size limit
	batches := splitBatch(preRows, MaxBigQueryBatchSize)
	totalSplits := len(batches) - 1 // Number of splits = number of batches - 1

	if len(batches) > 1 {
		logger.Debugf("Processing %d sub-batches in parallel for table %s", len(batches), q.table)
	}

	processingTime := time.Since(processingStart)

	// Track loading time (BigQuery operations)
	loadingStart := time.Now()

	// Process each sub-batch and collect metrics
	successCount := 0
	totalDLQMessages := 0
	totalDLQBytes := 0
	var lastError string

	// Process sub-batches in parallel with a concurrency limit
	concurrencyLimit := q.cfg.MaxConcurrency
	if concurrencyLimit <= 0 {
		concurrencyLimit = defaultMaxConcurrency // Use global default
	}
	semaphore := make(chan struct{}, concurrencyLimit)

	// Use a WaitGroup to wait for all sub-batches to complete
	var wg sync.WaitGroup
	results := make([]struct {
		dlqMessages int
		dlqBytes    int
		err         error
		index       int
	}, len(batches))

	for i, batch := range batches {
		wg.Add(1)
		go func(batchIndex int, batch []PreSerializedRow) {
			defer wg.Done()

			// Acquire semaphore to limit concurrency
			semaphore <- struct{}{}
			defer func() { <-semaphore }()

			dlqMessages, dlqBytes, err := q.loadSubBatch(batch, tablename, retryCount, batchIndex, len(batches))

			// Store results in thread-safe manner
			results[batchIndex] = struct {
				dlqMessages int
				dlqBytes    int
				err         error
				index       int
			}{dlqMessages, dlqBytes, err, batchIndex}
		}(i, batch)
	}

	// Wait for all sub-batches to complete
	wg.Wait()

	// Process results in order
	for _, result := range results {
		if result.err != nil {
			logger.Errorf("Failed to load sub-batch %d/%d: %v", result.index+1, len(batches), result.err)
			lastError = result.err.Error()
			// Continue with other batches even if one fails
		} else {
			successCount++
		}
		totalDLQMessages += result.dlqMessages
		totalDLQBytes += result.dlqBytes
	}

	loadingTime := time.Since(loadingStart)
	totalTime := time.Since(startTime)

	// Record batch performance stats
	totalBytes := 0
	for _, row := range preRows {
		totalBytes += row.Size
	}

	// Create and add performance stat record
	// Note: We only call updateMetrics once per batch operation, not per sub-batch
	// This ensures we get the correct wall-clock timing for parallel processing
	stat := schemas.BatchPerformanceStats{
		Table:           q.table,
		Timestamp:       time.Now().UTC(),
		BatchSize:       originalRows,
		BatchBytes:      totalBytes,
		ProcessingMs:    processingTime.Milliseconds(),
		LoadingMs:       loadingTime.Milliseconds(), // Wall-clock time for parallel processing
		TotalDurationMs: totalTime.Milliseconds(),   // Wall-clock time for parallel processing
		Retries:         retryCount,
		Error:           lastError,
		Splits:          totalSplits,
		DLQMessages:     totalDLQMessages,
		DLQBytes:        totalDLQBytes,
	}
	q.updateMetrics(stat)
}

// loadSubBatch performs the BigQuery batch load for a single sub-batch using pre-serialized rows
// Returns DLQ messages count, DLQ bytes, and error
func (q *queue) loadSubBatch(rows []PreSerializedRow, tablename string, retryCount int, batchIndex, totalBatches int) (int, int, error) {
	// Write pre-serialized JSONL to buffer
	var buf bytes.Buffer
	var rowMaps []map[string]bigquery.Value
	for _, r := range rows {
		buf.Write(r.JSONBytes)
		buf.WriteByte('\n')
		rowMaps = append(rowMaps, r.RowMap)
	}

	// set up reader source
	rs := bigquery.NewReaderSource(&buf)
	rs.SourceFormat = bigquery.JSON

	// configure loader
	dataset := q.exec.GetClient().Dataset(q.exec.GetConfig().DBName)
	t := dataset.Table(tablename)
	loader := t.LoaderFrom(rs)
	loader.WriteDisposition = bigquery.WriteAppend

	// run job with retry
	var lastErr error
	for i := range MaxAttempts {
		job, err := loader.Run(q.loadCtx)
		if err != nil {
			lastErr = fmt.Errorf("loader.Run failed: %w", err)
		} else {
			status, err := job.Wait(q.loadCtx)
			if err != nil {
				lastErr = fmt.Errorf("job.Wait failed: %w", err)
			} else if statusErr := status.Err(); statusErr != nil {
				msg := fmt.Sprintf("BQ job status error: %v", statusErr)
				for _, err := range status.Errors {
					msg += fmt.Sprintf("BQ row error: %s (%s)", err.Message, err.Reason)
				}
				lastErr = fmt.Errorf("%s", msg)
			} else {
				return 0, 0, nil // success
			}
		}

		// exponential backoff
		logger.Warnf("BqBatch load failed sub-batch %d/%d for table %s, retrying in %d seconds, error: %v",
			batchIndex+1, totalBatches, tablename, 1<<i, lastErr)
		timeSleep(1 << i * time.Second)
	}

	// send to pubsub DLQ
	logger.Errorf("BqBatch failed after %d retries for sub-batch %d/%d. Sending to pubsub batch DLQ",
		MaxAttempts, batchIndex+1, totalBatches)

	// convert bigquery.Values to any
	psData := make([]map[string]any, 0, len(rowMaps))
	for _, row := range rowMaps {
		rowMap := make(map[string]any, len(row))
		for k, v := range row {
			rowMap[k] = v
		}
		psData = append(psData, rowMap)
	}

	batch := FailedBatch{
		Table:      q.table,
		Timestamp:  time.Now().UTC(),
		Error:      lastErr.Error(),
		Rows:       psData,
		RetryCount: retryCount + 1,
	}

	// Split DLQ batch if it exceeds PubSub message size limit
	dlqBatches, err := splitDLQBatch(batch, MaxPubSubMessageSize)
	if err != nil {
		logger.Errorf("Failed to split DLQ batch: %v", err)
		return 0, 0, err
	}

	// Send each DLQ sub-batch
	dlqMessages := 0
	dlqBytes := 0
	for i, dlqBatch := range dlqBatches {
		if err := q.sendDLQMessage(dlqBatch, batchIndex+1, totalBatches, i+1, len(dlqBatches)); err != nil {
			logger.Errorf("Failed to send DLQ sub-batch %d/%d: %v", i+1, len(dlqBatches), err)
			// Continue with other DLQ batches even if one fails
		} else {
			dlqMessages++
			// Calculate DLQ message size
			if data, err := json.Marshal(dlqBatch); err == nil {
				dlqBytes += len(data)
			}
		}
	}

	return dlqMessages, dlqBytes, lastErr
}

// Load batch immediately loads a FailedBatch and executes a load.
func (b *batcher) LoadBatch(batch FailedBatch) error {
	q, ok := b.queues[batch.Table]
	if !ok {
		return fmt.Errorf("unknown table: %s", batch.Table)
	}

	var savers []bigquery.ValueSaver
	for _, row := range batch.Rows {
		savers = append(savers, &bigquery.StructSaver{
			Struct: row,
			Schema: q.schema,
		})
	}

	// q.load already handles batch splitting internally
	go q.load(savers, batch.RetryCount)
	logger.Debugf("Scheduled batch for table %s with %d rows", batch.Table, len(savers))

	return nil
}

// Add the Connections to the context.
func WithBatch(ctx context.Context, batch Batcher) context.Context {
	return context.WithValue(ctx, BatchKey, batch)
}

// Get the Connections from the context.
// checkConnections is optional
var GetBatch = func(ctx context.Context) (Batcher, error) {
	batch, ok := ctx.Value(BatchKey).(Batcher)
	if !ok {
		return nil, ErrBatchContext
	}

	return batch, nil
}

// preSerializeDLQRows serializes each DLQ row once and returns PreSerializedDLQRow slices
func preSerializeDLQRows(rows []map[string]any) ([]PreSerializedDLQRow, error) {
	var result []PreSerializedDLQRow
	for _, row := range rows {
		jsonBytes, err := json.Marshal(row)
		if err != nil {
			return nil, err
		}
		result = append(result, PreSerializedDLQRow{
			RowMap:    row,
			JSONBytes: jsonBytes,
			Size:      len(jsonBytes) + 1, // +1 for comma or newline
		})
	}
	return result, nil
}

// splitDLQBatch splits a FailedBatch into smaller batches that fit within PubSub message size limits
func splitDLQBatch(batch FailedBatch, maxSize int) ([]FailedBatch, error) {
	if len(batch.Rows) == 0 {
		return []FailedBatch{batch}, nil
	}

	preRows, err := preSerializeDLQRows(batch.Rows)
	if err != nil {
		return nil, err
	}

	var batches []FailedBatch
	var currentRows []map[string]any
	var currentSize int

	// Calculate base batch size without rows
	baseBatch := FailedBatch{
		Table:      batch.Table,
		Timestamp:  batch.Timestamp,
		Error:      batch.Error,
		RetryCount: batch.RetryCount,
		Rows:       []map[string]any{},
	}
	baseData, err := json.Marshal(baseBatch)
	if err != nil {
		return nil, err
	}
	baseSize := len(baseData)

	for _, row := range preRows {
		if currentSize+row.Size+baseSize > maxSize && len(currentRows) > 0 {
			subBatch := baseBatch
			subBatch.Rows = currentRows
			batches = append(batches, subBatch)
			currentRows = []map[string]any{row.RowMap}
			currentSize = row.Size
		} else {
			currentRows = append(currentRows, row.RowMap)
			currentSize += row.Size
		}
	}
	if len(currentRows) > 0 {
		subBatch := baseBatch
		subBatch.Rows = currentRows
		batches = append(batches, subBatch)
	}
	return batches, nil
}

// sendDLQMessage sends a FailedBatch to the PubSub DLQ topic
func (q *queue) sendDLQMessage(batch FailedBatch, batchIndex, totalBatches, dlqIndex, totalDLQ int) error {
	data, err := json.Marshal(batch)
	if err != nil {
		return fmt.Errorf("failed to marshal DLQ batch: %w", err)
	}

	topic := q.psClient.Topic(PubsubDLQTopic)

	for i := range MaxAttempts {
		res := topic.Publish(q.loadCtx, &pubsub.Message{
			Data: data,
			Attributes: map[string]string{
				"table":         batch.Table,
				"retry_count":   strconv.Itoa(batch.RetryCount),
				"error":         batch.Error,
				"batch_index":   strconv.Itoa(batchIndex),
				"total_batches": strconv.Itoa(totalBatches),
				"dlq_index":     strconv.Itoa(dlqIndex),
				"total_dlq":     strconv.Itoa(totalDLQ),
			},
		})
		_, err := res.Get(q.loadCtx)
		if err == nil {
			return nil
		}

		// exponential backoff
		logger.Warnf("BqBatch backup pubsub DLQ failed for sub-batch %d/%d, retrying in %d seconds, error: %v",
			dlqIndex, totalDLQ, 1<<i, err)
		timeSleep(1 << i * time.Second)
	}

	logger.Error("BqBatch failed to send to DLQ backup. Dumping batch to logs...")
	logger.Error(data)
	return fmt.Errorf("failed to send DLQ message after %d retries", MaxAttempts)
}

// Update performance metrics after each batch load
func (q *queue) updateMetrics(stat schemas.BatchPerformanceStats) {
	q.metricsMu.Lock()
	defer q.metricsMu.Unlock()
	m := &q.metrics
	m.BatchSize += stat.BatchSize
	m.BatchBytes += stat.BatchBytes
	m.ProcessingMs += stat.ProcessingMs

	m.LoadingMs += stat.LoadingMs
	m.TotalDurationMs += stat.TotalDurationMs

	m.Retries += stat.Retries
	m.Splits += stat.Splits
	m.DLQMessages += stat.DLQMessages
	m.DLQBytes += stat.DLQBytes

	if stat.Error != "" {
		m.Error = stat.Error
	}
	if m.Timestamp.IsZero() || stat.Timestamp.After(m.Timestamp) {
		m.Timestamp = stat.Timestamp
	}
}

// Flush the performance metrics to BigQuery if changed
func (q *queue) flushMetrics() {
	q.metricsMu.Lock()
	stat := q.metrics
	q.metricsMu.Unlock()

	// Only flush if metrics have changed (nonzero)
	if stat.BatchSize == 0 && stat.BatchBytes == 0 && stat.ProcessingMs == 0 && stat.LoadingMs == 0 && stat.TotalDurationMs == 0 && stat.Retries == 0 && stat.Splits == 0 && stat.DLQMessages == 0 && stat.DLQBytes == 0 {
		return
	}

	// Add metrics to BigQuery via batcher.Add()
	_ = q.batcher.Add(stat)

	// Reset metrics
	q.metricsMu.Lock()
	q.metrics = schemas.BatchPerformanceStats{Table: q.table}
	q.metricsMu.Unlock()
}
