package middleware

import (
	"context"
	"net/http"

	"golang.org/x/oauth2"
	"synapse-its.com/shared/rest/onramp/session"
)

type SessionData struct {
	UserID          string
	OAuthToken      *oauth2.Token
	UserPermissions any
}

// SessionMiddleware injects session data from cookie into context
func SessionMiddleware(store session.SessionStore) func(http.Handler) http.Handler {
	return func(next http.Handler) http.Handler {
		return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			c, err := r.<PERSON><PERSON>("session_id")
			if err != nil {
				next.ServeHTTP(w, r)
				return
			}
			sessionData, ok := store.GetSession(c.Value)
			if !ok {
				next.ServeHTTP(w, r)
				return
			}
			ctx := context.WithValue(r.Context(), "session", sessionData)
			next.ServeHTTP(w, r.WithContext(ctx))
		})
	}
}
