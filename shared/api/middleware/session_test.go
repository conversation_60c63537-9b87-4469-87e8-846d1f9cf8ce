package middleware

import (
	"net/http"
	"net/http/httptest"
	"testing"

	"synapse-its.com/shared/rest/onramp/session"
)

func TestSessionMiddleware(t *testing.T) {
	tests := []struct {
		name           string
		setupSession   bool
		cookieValue    string
		expectInjected bool
	}{
		{
			name:           "Valid session",
			setupSession:   true,
			cookieValue:    "valid-session-id",
			expectInjected: true,
		},
		{
			name:           "Missing cookie",
			setupSession:   false,
			cookieValue:    "",
			expectInjected: false,
		},
		{
			name:           "Invalid session ID",
			setupSession:   false,
			cookieValue:    "invalid-session-id",
			expectInjected: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Create a single session store instance for each test
			store := session.NewSessionStore()

			if tt.setupSession {
				store.SetSession(tt.cookieValue, &session.SessionData{
					UserID: "user123",
					Data:   make(map[string]any),
				})
			}

			req := httptest.NewRequest(http.MethodGet, "/", nil)
			if tt.cookieValue != "" {
				req.AddCookie(&http.Cookie{
					Name:  "session_id",
					Value: tt.cookieValue,
				})
			}
			rec := httptest.NewRecorder()

			next := http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
				ctx := r.Context()
				s := ctx.Value("session")
				if tt.expectInjected && s == nil {
					t.Errorf("Expected session injected in context but got nil")
				}
				if !tt.expectInjected && s != nil {
					t.Errorf("Expected no session injected but got one")
				}
				w.WriteHeader(http.StatusOK)
			})

			// Use the same session store instance for the middleware
			middleware := SessionMiddleware(store)(next)
			middleware.ServeHTTP(rec, req)
		})
	}
}
