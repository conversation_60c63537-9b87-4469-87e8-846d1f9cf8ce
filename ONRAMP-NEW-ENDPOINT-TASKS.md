# Adding New Endpoint to Onramp Microservice

This document tracks the process of adding a new endpoint to the onramp microservice.

## Project Structure Analysis

The onramp microservice follows a modular architecture with the following key components:

### Completed Tasks

- [x] Analyzed main.go - Entry point with dependency injection pattern
- [x] Analyzed app/app.go - Application setup and route registration
- [x] Analyzed app/routes.go - Router initialization and middleware setup
- [x] Analyzed handlers/ - Simple HTTP handlers for basic endpoints
- [x] Analyzed modules/ - Modular structure for complex features (auth, organization, softwaregateway)
- [x] Analyzed domain/ - Domain models and interfaces
- [x] Analyzed middlewares/ - Authentication and other middleware
- [x] Analyzed pkg/ - Utility packages

### Key Architecture Patterns Discovered

1. **Dependency Injection**: Main function uses dependency injection for testability
2. **Modular Structure**: Complex features are organized in modules/ directory
3. **Simple Handlers**: Basic endpoints use handlers/ directory
4. **Middleware Pattern**: Authentication and logging middleware applied globally
5. **Route Registration**: Routes are registered in app.Serve() method
6. **Protected Routes**: Some routes use authentication middleware

## Implementation Plan

### Option 1: Simple Endpoint (Recommended for basic functionality)
- Add handler to `handlers/` directory
- Register route in `app/app.go` Serve() method
- Follow existing patterns in foo.go, gateway.go, devices.go

### Option 2: Complex Endpoint (Recommended for business logic)
- Create new module in `modules/` directory
- Follow auth module pattern with handler, service, repository layers
- Register routes in module's RegisterRoutes method

## Relevant Files

- `main.go` - Application entry point and dependency injection
- `app/app.go` - Application setup and route registration
- `app/routes.go` - Router initialization and middleware
- `handlers/` - Simple HTTP handlers
- `modules/` - Complex feature modules
- `domain/` - Domain models and interfaces
- `middlewares/` - Authentication and middleware
- `pkg/` - Utility packages

## Next Steps

- [ ] Choose endpoint type (simple vs complex)
- [ ] Create handler implementation
- [ ] Register route in application
- [ ] Add tests
- [ ] Update documentation 